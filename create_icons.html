<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Icon Generator for WhatsApp Exporter</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon-preview {
            display: inline-block;
            margin: 10px;
            text-align: center;
        }
        .icon {
            border: 1px solid #ddd;
            margin-bottom: 5px;
        }
        canvas {
            border: 1px solid #ccc;
        }
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #20b358;
        }
        .instructions {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WhatsApp Exporter Icon Generator</h1>
        
        <div class="instructions">
            <h3>Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" to create the icon files</li>
                <li>Right-click each icon and "Save image as..."</li>
                <li>Save them in the <code>icons/</code> folder with the correct names:
                    <ul>
                        <li>icon16.png (16x16)</li>
                        <li>icon48.png (48x48)</li>
                        <li>icon128.png (128x128)</li>
                    </ul>
                </li>
            </ol>
        </div>
        
        <button onclick="generateIcons()">Generate Icons</button>
        <button onclick="downloadAll()">Download All Icons</button>
        
        <div id="iconContainer">
            <!-- Icons will be generated here -->
        </div>
    </div>

    <script>
        function generateIcons() {
            const container = document.getElementById('iconContainer');
            container.innerHTML = '';
            
            const sizes = [16, 48, 128];
            
            sizes.forEach(size => {
                const preview = document.createElement('div');
                preview.className = 'icon-preview';
                
                const canvas = document.createElement('canvas');
                canvas.width = size;
                canvas.height = size;
                canvas.className = 'icon';
                
                const ctx = canvas.getContext('2d');
                
                // Create WhatsApp-style icon
                createWhatsAppIcon(ctx, size);
                
                const label = document.createElement('div');
                label.textContent = `${size}x${size}`;
                
                const downloadBtn = document.createElement('button');
                downloadBtn.textContent = `Download icon${size}.png`;
                downloadBtn.onclick = () => downloadIcon(canvas, `icon${size}.png`);
                
                preview.appendChild(canvas);
                preview.appendChild(label);
                preview.appendChild(downloadBtn);
                container.appendChild(preview);
            });
        }
        
        function createWhatsAppIcon(ctx, size) {
            // Background circle
            ctx.fillStyle = '#25D366';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 1, 0, 2 * Math.PI);
            ctx.fill();
            
            // White chat bubble
            const bubbleSize = size * 0.6;
            const bubbleX = size * 0.2;
            const bubbleY = size * 0.2;
            
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.roundRect(bubbleX, bubbleY, bubbleSize, bubbleSize * 0.7, size * 0.1);
            ctx.fill();
            
            // Excel-style grid lines
            ctx.strokeStyle = '#25D366';
            ctx.lineWidth = Math.max(1, size * 0.02);
            
            // Horizontal lines
            for (let i = 1; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(bubbleX + bubbleSize * 0.1, bubbleY + (bubbleSize * 0.7 * i / 3));
                ctx.lineTo(bubbleX + bubbleSize * 0.9, bubbleY + (bubbleSize * 0.7 * i / 3));
                ctx.stroke();
            }
            
            // Vertical lines
            for (let i = 1; i < 3; i++) {
                ctx.beginPath();
                ctx.moveTo(bubbleX + (bubbleSize * i / 3), bubbleY + bubbleSize * 0.1);
                ctx.lineTo(bubbleX + (bubbleSize * i / 3), bubbleY + bubbleSize * 0.6);
                ctx.stroke();
            }
            
            // Small export arrow
            if (size >= 48) {
                ctx.fillStyle = '#128C7E';
                ctx.beginPath();
                ctx.moveTo(size * 0.7, size * 0.75);
                ctx.lineTo(size * 0.85, size * 0.75);
                ctx.lineTo(size * 0.8, size * 0.85);
                ctx.lineTo(size * 0.9, size * 0.85);
                ctx.lineTo(size * 0.75, size * 0.95);
                ctx.lineTo(size * 0.6, size * 0.85);
                ctx.lineTo(size * 0.7, size * 0.85);
                ctx.closePath();
                ctx.fill();
            }
        }
        
        function downloadIcon(canvas, filename) {
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function downloadAll() {
            const canvases = document.querySelectorAll('canvas');
            const sizes = [16, 48, 128];
            
            canvases.forEach((canvas, index) => {
                setTimeout(() => {
                    downloadIcon(canvas, `icon${sizes[index]}.png`);
                }, index * 500);
            });
        }
        
        // Add roundRect polyfill for older browsers
        if (!CanvasRenderingContext2D.prototype.roundRect) {
            CanvasRenderingContext2D.prototype.roundRect = function(x, y, width, height, radius) {
                this.beginPath();
                this.moveTo(x + radius, y);
                this.lineTo(x + width - radius, y);
                this.quadraticCurveTo(x + width, y, x + width, y + radius);
                this.lineTo(x + width, y + height - radius);
                this.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
                this.lineTo(x + radius, y + height);
                this.quadraticCurveTo(x, y + height, x, y + height - radius);
                this.lineTo(x, y + radius);
                this.quadraticCurveTo(x, y, x + radius, y);
                this.closePath();
            };
        }
    </script>
</body>
</html>
