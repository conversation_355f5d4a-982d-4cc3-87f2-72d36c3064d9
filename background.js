// Background script for WhatsApp Web Data Exporter

chrome.runtime.onInstalled.addListener((details) => {
  if (details.reason === 'install') {
    console.log('WhatsApp Web Data Exporter installed');
    
    // Open welcome page or instructions
    chrome.tabs.create({
      url: 'https://web.whatsapp.com'
    });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener((tab) => {
  // Check if we're on WhatsApp Web
  if (!tab.url.includes('web.whatsapp.com')) {
    // If not on WhatsApp Web, open it
    chrome.tabs.create({
      url: 'https://web.whatsapp.com'
    });
  }
});

// Listen for tab updates to enable/disable extension
chrome.tabs.onUpdated.addListener((tabId, changeInfo, tab) => {
  if (changeInfo.status === 'complete' && tab.url) {
    if (tab.url.includes('web.whatsapp.com')) {
      // Enable extension icon
      chrome.action.enable(tabId);
      chrome.action.setBadgeText({
        text: '',
        tabId: tabId
      });
    } else {
      // Disable extension icon for non-WhatsApp tabs
      chrome.action.disable(tabId);
      chrome.action.setBadgeText({
        text: '!',
        tabId: tabId
      });
      chrome.action.setBadgeBackgroundColor({
        color: '#FF0000',
        tabId: tabId
      });
    }
  }
});

// Handle messages from content script
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  if (request.action === 'downloadFile') {
    chrome.downloads.download({
      url: request.url,
      filename: request.filename,
      saveAs: true
    }, (downloadId) => {
      if (chrome.runtime.lastError) {
        sendResponse({ success: false, error: chrome.runtime.lastError.message });
      } else {
        sendResponse({ success: true, downloadId: downloadId });
      }
    });
    return true; // Keep message channel open for async response
  }
});

// Set up context menu (optional)
chrome.contextMenus.create({
  id: 'exportWhatsAppData',
  title: 'Export WhatsApp Data',
  contexts: ['page'],
  documentUrlPatterns: ['https://web.whatsapp.com/*']
});

chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === 'exportWhatsAppData') {
    // Open the popup or trigger export
    chrome.action.openPopup();
  }
});

console.log('WhatsApp Web Data Exporter background script loaded');
