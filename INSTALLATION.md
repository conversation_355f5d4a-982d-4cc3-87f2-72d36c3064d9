# Installation Guide - WhatsApp Web Data Exporter

## Quick Start

### Step 1: Prepare the Extension
1. Make sure all files are in the same folder:
   - `manifest.json`
   - `content.js`
   - `popup.html`
   - `popup.css`
   - `popup.js`
   - `background.js`
   - `icons/` folder (with icon files)

### Step 2: Create Icons (Required)
1. Open `create_icons.html` in your browser
2. Click "Generate Icons"
3. Download each icon and save them in the `icons/` folder:
   - `icon16.png`
   - `icon48.png`
   - `icon128.png`

### Step 3: Install in Chrome
1. Open Chrome and go to `chrome://extensions/`
2. Enable "Developer mode" (toggle in top-right)
3. Click "Load unpacked"
4. Select the folder containing all the extension files
5. The extension should now appear in your extensions list

### Step 4: Pin the Extension
1. Click the puzzle piece icon in Chrome toolbar
2. Find "WhatsApp Web Data Exporter"
3. Click the pin icon to keep it visible

## Usage Instructions

### For Current Chat Export:
1. Go to `https://web.whatsapp.com`
2. Open the chat you want to export
3. Click the extension icon
4. Select your export options
5. Click "Export Current Chat"
6. Wait for the Excel file to download

### For Contacts Export:
1. Go to `https://web.whatsapp.com`
2. Click the extension icon
3. Click "Export All Contacts"
4. Wait for the Excel file to download

## Troubleshooting

### Extension Not Loading
- Check that all files are in the correct location
- Make sure Developer mode is enabled
- Try refreshing the extensions page

### Icons Not Showing
- Make sure you have created the icon files using `create_icons.html`
- Icons must be named exactly: `icon16.png`, `icon48.png`, `icon128.png`
- Icons must be in the `icons/` subfolder

### Export Not Working
- Make sure you're on `web.whatsapp.com`
- Refresh the WhatsApp page
- Check that the chat is fully loaded
- Try reloading the extension

### Permission Errors
- The extension only works on WhatsApp Web
- Make sure you've granted necessary permissions
- Check Chrome's extension settings

## File Structure
```
WhatsApp-Exporter/
├── manifest.json
├── content.js
├── popup.html
├── popup.css
├── popup.js
├── background.js
├── README.md
├── INSTALLATION.md
├── create_icons.html
└── icons/
    ├── icon16.png
    ├── icon48.png
    └── icon128.png
```

## Next Steps
Once installed, you can:
- Export individual chats with full message history
- Export contact lists
- Include or exclude timestamps, attachments, phone numbers
- Get data in Excel format for further analysis

## Support
If you encounter issues:
1. Check the troubleshooting section above
2. Verify all files are present and correctly named
3. Check Chrome's developer console for errors
4. Try reinstalling the extension
