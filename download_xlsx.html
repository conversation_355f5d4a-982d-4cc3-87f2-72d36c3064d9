<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Download XLSX Library</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #25D366;
            background: #f8f9fa;
        }
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #20b358;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #721c24;
        }
        code {
            background: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        #status {
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Download XLSX Library for WhatsApp Exporter</h1>
        
        <div class="step">
            <h3>Option 1: Automatic Download (Recommended)</h3>
            <p>Click the button below to automatically download the XLSX library:</p>
            <button onclick="downloadXLSX()">Download XLSX Library</button>
            <div id="status"></div>
        </div>

        <div class="step">
            <h3>Option 2: Manual Download</h3>
            <p>If automatic download doesn't work:</p>
            <ol>
                <li>Go to: <a href="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js" target="_blank">XLSX Library Link</a></li>
                <li>Right-click and "Save as..." to your extension folder</li>
                <li>Name the file: <code>xlsx.full.min.js</code></li>
                <li>Update the popup.html to use the local file</li>
            </ol>
        </div>

        <div class="step">
            <h3>Option 3: Use CSV Export (No Additional Setup)</h3>
            <p>The extension now includes a CSV fallback that works without any external libraries.</p>
            <p>If Excel export fails, it will automatically export as CSV format instead.</p>
        </div>

        <div class="step">
            <h3>After Download</h3>
            <p>Once you have the XLSX library:</p>
            <ol>
                <li>Place <code>xlsx.full.min.js</code> in your extension folder</li>
                <li>Update <code>popup.html</code> to include: <code>&lt;script src="xlsx.full.min.js"&gt;&lt;/script&gt;</code></li>
                <li>Reload the extension in Chrome</li>
                <li>Test the export functionality</li>
            </ol>
        </div>
    </div>

    <script>
        async function downloadXLSX() {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = '<div style="color: blue;">Downloading XLSX library...</div>';
            
            try {
                // Fetch the XLSX library
                const response = await fetch('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js');
                
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                
                const xlsxContent = await response.text();
                
                // Create a blob and download link
                const blob = new Blob([xlsxContent], { type: 'application/javascript' });
                const url = URL.createObjectURL(blob);
                
                const a = document.createElement('a');
                a.href = url;
                a.download = 'xlsx.full.min.js';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                
                statusDiv.innerHTML = `
                    <div class="success">
                        ✅ XLSX library downloaded successfully!<br>
                        📁 File saved as: <code>xlsx.full.min.js</code><br>
                        📝 Next: Place this file in your extension folder and update popup.html
                    </div>
                `;
                
                // Show instructions for updating popup.html
                setTimeout(() => {
                    statusDiv.innerHTML += `
                        <div style="background: #e8f4f8; padding: 15px; border-radius: 5px; margin-top: 10px;">
                            <strong>Update popup.html:</strong><br>
                            Add this line before the closing &lt;/body&gt; tag:<br>
                            <code>&lt;script src="xlsx.full.min.js"&gt;&lt;/script&gt;</code><br>
                            <code>&lt;script src="popup.js"&gt;&lt;/script&gt;</code>
                        </div>
                    `;
                }, 1000);
                
            } catch (error) {
                console.error('Download failed:', error);
                statusDiv.innerHTML = `
                    <div class="error">
                        ❌ Download failed: ${error.message}<br>
                        💡 Try the manual download option instead
                    </div>
                `;
            }
        }
        
        // Test if we can access the CDN
        window.addEventListener('load', async () => {
            try {
                const response = await fetch('https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js', { method: 'HEAD' });
                if (response.ok) {
                    console.log('CDN is accessible');
                } else {
                    document.getElementById('status').innerHTML = `
                        <div style="color: orange;">
                            ⚠️ CDN may not be accessible. Try manual download if automatic fails.
                        </div>
                    `;
                }
            } catch (error) {
                document.getElementById('status').innerHTML = `
                    <div style="color: orange;">
                        ⚠️ Network connectivity issue. Manual download recommended.
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
