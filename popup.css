* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #25D366 0%, #128C7E 100%);
    color: #333;
    width: 400px;
    min-height: 500px;
}

.container {
    background: white;
    border-radius: 12px;
    margin: 8px;
    padding: 20px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.header {
    text-align: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 2px solid #f0f0f0;
}

.logo {
    width: 32px;
    height: 32px;
    margin-bottom: 8px;
}

h1 {
    font-size: 18px;
    color: #128C7E;
    font-weight: 600;
}

.status {
    background: #e8f5e8;
    border: 1px solid #25D366;
    border-radius: 8px;
    padding: 12px;
    margin-bottom: 20px;
    text-align: center;
}

.status.error {
    background: #ffe8e8;
    border-color: #ff4444;
    color: #cc0000;
}

.status.success {
    background: #e8f5e8;
    border-color: #25D366;
    color: #128C7E;
}

.export-options {
    margin-bottom: 20px;
}

.export-options h3 {
    font-size: 14px;
    margin-bottom: 12px;
    color: #128C7E;
}

.option-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.checkbox-container {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 13px;
    padding: 4px 0;
}

.checkbox-container input[type="checkbox"] {
    margin-right: 8px;
    transform: scale(1.1);
}

.export-buttons {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin-bottom: 20px;
}

.btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 12px 16px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: #25D366;
    color: white;
}

.btn-primary:hover {
    background: #20b358;
}

.btn-secondary {
    background: #128C7E;
    color: white;
}

.btn-secondary:hover {
    background: #0f7a6b;
}

.btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.btn-icon {
    font-size: 16px;
}

.progress {
    margin-bottom: 20px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #f0f0f0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 8px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #25D366, #128C7E);
    width: 0%;
    transition: width 0.3s ease;
}

#progressText {
    font-size: 12px;
    text-align: center;
    color: #666;
}

.info {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.info h4 {
    font-size: 13px;
    margin-bottom: 8px;
    color: #128C7E;
}

.info ol {
    font-size: 12px;
    line-height: 1.5;
    padding-left: 16px;
}

.info li {
    margin-bottom: 4px;
}

.warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 6px;
    padding: 10px;
    margin-top: 12px;
    font-size: 11px;
    color: #856404;
}

.footer {
    text-align: center;
    font-size: 11px;
    color: #999;
    border-top: 1px solid #f0f0f0;
    padding-top: 10px;
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.container {
    animation: fadeIn 0.3s ease;
}

/* Responsive adjustments */
@media (max-width: 420px) {
    body {
        width: 100vw;
    }
    
    .container {
        margin: 4px;
        padding: 15px;
    }
}
