<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Extension Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .step {
            margin: 20px 0;
            padding: 15px;
            border-left: 4px solid #25D366;
            background: #f8f9fa;
        }
        .step h3 {
            margin-top: 0;
            color: #128C7E;
        }
        .warning {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #856404;
        }
        .success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            padding: 15px;
            margin: 15px 0;
            color: #721c24;
        }
        button {
            background: #25D366;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #20b358;
        }
        code {
            background: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
        .checklist {
            list-style-type: none;
            padding: 0;
        }
        .checklist li {
            margin: 10px 0;
            padding: 5px;
        }
        .checklist li:before {
            content: "☐ ";
            margin-right: 10px;
        }
        .checklist li.checked:before {
            content: "☑ ";
            color: green;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WhatsApp Web Data Exporter - Installation Test</h1>
        
        <div class="warning">
            <strong>Important:</strong> This page helps you test and troubleshoot the WhatsApp Web Data Exporter extension.
        </div>

        <div class="step">
            <h3>Step 1: Extension Installation Checklist</h3>
            <ul class="checklist" id="installChecklist">
                <li data-check="files">All extension files are in the same folder</li>
                <li data-check="icons">Icon files created and placed in icons/ folder</li>
                <li data-check="developer">Developer mode enabled in Chrome</li>
                <li data-check="loaded">Extension loaded as unpacked</li>
                <li data-check="pinned">Extension pinned to toolbar</li>
            </ul>
            <button onclick="checkInstallation()">Check Installation</button>
        </div>

        <div class="step">
            <h3>Step 2: WhatsApp Web Connection Test</h3>
            <p>Click the button below to open WhatsApp Web in a new tab:</p>
            <button onclick="openWhatsApp()">Open WhatsApp Web</button>
            <div id="whatsappStatus" class="warning" style="display: none;">
                Please log in to WhatsApp Web and open a chat before testing the extension.
            </div>
        </div>

        <div class="step">
            <h3>Step 3: Extension Functionality Test</h3>
            <p>After opening WhatsApp Web and selecting a chat:</p>
            <ol>
                <li>Click the extension icon in your Chrome toolbar</li>
                <li>The popup should show "Ready to export WhatsApp data"</li>
                <li>If you see an error, try refreshing the WhatsApp page</li>
                <li>Try exporting a small chat first</li>
            </ol>
        </div>

        <div class="step">
            <h3>Step 4: Troubleshooting</h3>
            <div id="troubleshootingResults"></div>
            <button onclick="runDiagnostics()">Run Diagnostics</button>
        </div>

        <div class="step">
            <h3>Common Issues & Solutions</h3>
            
            <h4>"Could not establish connection" Error:</h4>
            <ul>
                <li>Refresh the WhatsApp Web page</li>
                <li>Make sure you're on <code>web.whatsapp.com</code></li>
                <li>Try reloading the extension in <code>chrome://extensions/</code></li>
                <li>Check that the extension has proper permissions</li>
            </ul>

            <h4>"Content script not ready" Error:</h4>
            <ul>
                <li>Wait for WhatsApp Web to fully load</li>
                <li>Open a chat conversation</li>
                <li>Refresh the page and try again</li>
            </ul>

            <h4>"Messages container not found" Error:</h4>
            <ul>
                <li>Make sure a chat is open and visible</li>
                <li>Scroll up to load more messages</li>
                <li>Try a different chat</li>
            </ul>
        </div>

        <div class="step">
            <h3>File Structure Check</h3>
            <p>Your extension folder should contain these files:</p>
            <ul>
                <li><code>manifest.json</code></li>
                <li><code>content.js</code></li>
                <li><code>popup.html</code></li>
                <li><code>popup.css</code></li>
                <li><code>popup.js</code></li>
                <li><code>background.js</code></li>
                <li><code>icons/icon16.png</code></li>
                <li><code>icons/icon48.png</code></li>
                <li><code>icons/icon128.png</code></li>
            </ul>
        </div>
    </div>

    <script>
        function checkInstallation() {
            const checklist = document.getElementById('installChecklist');
            const items = checklist.querySelectorAll('li');
            
            // This is a manual checklist - in a real scenario, you'd check these programmatically
            items.forEach(item => {
                item.classList.add('checked');
            });
            
            alert('Please manually verify each item in the checklist. Click each item to mark it as complete.');
        }

        function openWhatsApp() {
            window.open('https://web.whatsapp.com', '_blank');
            document.getElementById('whatsappStatus').style.display = 'block';
        }

        function runDiagnostics() {
            const results = document.getElementById('troubleshootingResults');
            
            let diagnostics = '<h4>Diagnostic Results:</h4>';
            
            // Check if we're on the right domain
            if (window.location.hostname !== 'web.whatsapp.com') {
                diagnostics += '<div class="warning">⚠️ You are not on web.whatsapp.com</div>';
            } else {
                diagnostics += '<div class="success">✅ Correct domain</div>';
            }
            
            // Check for Chrome extension API
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                diagnostics += '<div class="success">✅ Chrome extension API available</div>';
            } else {
                diagnostics += '<div class="error">❌ Chrome extension API not available</div>';
            }
            
            // Check for required DOM elements (if on WhatsApp)
            if (window.location.hostname === 'web.whatsapp.com') {
                const chatContainer = document.querySelector('[data-testid="conversation-panel-messages"]');
                if (chatContainer) {
                    diagnostics += '<div class="success">✅ Chat container found</div>';
                } else {
                    diagnostics += '<div class="warning">⚠️ Chat container not found - open a chat</div>';
                }
            }
            
            results.innerHTML = diagnostics;
        }

        // Make checklist items clickable
        document.addEventListener('DOMContentLoaded', function() {
            const checklistItems = document.querySelectorAll('.checklist li');
            checklistItems.forEach(item => {
                item.addEventListener('click', function() {
                    this.classList.toggle('checked');
                });
                item.style.cursor = 'pointer';
            });
        });
    </script>
</body>
</html>
