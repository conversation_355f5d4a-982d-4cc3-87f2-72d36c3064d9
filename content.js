// WhatsApp Web Data Extractor Content Script

class WhatsAppDataExtractor {
  constructor() {
    this.chatData = [];
    this.contactData = [];
    this.attachmentData = [];
  }

  // Wait for element to be available
  waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  // Wait for WhatsApp to fully load
  async waitForWhatsAppToLoad() {
    // Check if we're on WhatsApp Web
    if (!window.location.href.includes('web.whatsapp.com')) {
      throw new Error('Please navigate to web.whatsapp.com first');
    }

    // Wait for the main app to load
    const appSelectors = [
      '[data-testid="app-wrapper-web"]',
      '.app-wrapper-web',
      '#app',
      '[data-testid="main"]'
    ];

    let appLoaded = false;
    for (const selector of appSelectors) {
      if (document.querySelector(selector)) {
        appLoaded = true;
        break;
      }
    }

    if (!appLoaded) {
      throw new Error('WhatsApp Web is not fully loaded. Please wait for the page to load completely.');
    }

    // Check if user is logged in (no QR code visible)
    const qrCode = document.querySelector('[data-testid="qr-code"]');
    if (qrCode && qrCode.offsetParent !== null) {
      throw new Error('Please scan the QR code to log in to WhatsApp Web first.');
    }

    // Wait a moment for the interface to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Extract current chat messages
  async extractCurrentChatData(dateFilter = null) {
    try {
      // First, check if we're on the right page and a chat is open
      await this.waitForWhatsAppToLoad();

      // Wait a moment for the chat to be fully loaded
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find the ACTIVE/CURRENT chat container (not just any chat)
      const activeMessageSelectors = [
        '#main [data-testid="conversation-panel-messages"]',
        '#main [role="application"]',
        '[data-testid="main"] [data-testid="conversation-panel-messages"]',
        '[data-testid="main"] [role="application"]',
        '#main .copyable-area',
        '[data-testid="main"] .copyable-area'
      ];

      let messagesContainer = null;

      // Try each selector specifically for the main/active chat area
      for (const selector of activeMessageSelectors) {
        try {
          console.log(`Trying active chat selector: ${selector}`);
          messagesContainer = document.querySelector(selector);
          if (messagesContainer && this.isActiveChat(messagesContainer)) {
            console.log(`Found ACTIVE messages container with selector: ${selector}`);
            break;
          }
        } catch (e) {
          console.log(`Selector ${selector} failed:`, e);
        }
      }

      // If still not found, try waiting a bit more and check again
      if (!messagesContainer) {
        console.log('Messages container not found immediately, waiting and retrying...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        for (const selector of messageSelectors) {
          messagesContainer = document.querySelector(selector);
          if (messagesContainer) {
            console.log(`Found messages container on retry with selector: ${selector}`);
            break;
          }
        }
      }

      if (!messagesContainer) {
        // Try to find any element that might contain messages
        const fallbackSelectors = [
          '#main',
          '[data-testid="main"]',
          '.copyable-area',
          '[role="main"]'
        ];

        for (const selector of fallbackSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            console.log(`Using fallback container: ${selector}`);
            messagesContainer = element;
            break;
          }
        }
      }

      if (!messagesContainer) {
        throw new Error('No chat is currently open. Please open a chat conversation and try again.');
      }

      // Get chat header for the ACTIVE chat only
      const activeHeaderSelectors = [
        '#main [data-testid="conversation-header"]',
        '#main header[data-testid="conversation-header"]',
        '[data-testid="main"] [data-testid="conversation-header"]',
        '[data-testid="main"] header',
        '#main header',
        '#main [data-testid="chat-header"]'
      ];

      let chatHeader = null;
      for (const selector of activeHeaderSelectors) {
        chatHeader = document.querySelector(selector);
        if (chatHeader && this.isInActiveChat(chatHeader)) {
          console.log(`Found ACTIVE chat header with selector: ${selector}`);
          break;
        }
      }

      let contactName = this.extractContactName(chatHeader);
      let contactNumber = this.extractPhoneNumber(chatHeader);

      // Cross-validate phone number with current chat to ensure we have the right one
      contactNumber = this.validatePhoneNumberForActiveChat(contactNumber, contactName);

      // If contact name is still problematic, try to get it from phone number or other sources
      if (!contactName ||
          contactName === 'Unknown' ||
          contactName === 'search-refreshed' ||
          contactName.includes('search') ||
          contactName.includes('refresh')) {

        console.log('Contact name is problematic, trying alternative methods...');
        contactName = this.getContactNameFromPhoneNumber(contactNumber) ||
                     this.getContactNameFromPageTitle() ||
                     this.getContactNameFromChatList() ||
                     'Unknown Contact';
      }

      console.log(`Final ACTIVE chat info - Name: ${contactName}, Phone: ${contactNumber}`);

      // Get all message elements with multiple selectors
      const msgSelectors = [
        '[data-testid="msg-container"]',
        '[data-testid="msg"]',
        '.message-in, .message-out',
        '[role="row"]',
        'div[data-id]',
        '.copyable-text',
        '[data-testid*="msg"]'
      ];

      let messageElements = [];
      for (const selector of msgSelectors) {
        const foundElements = messagesContainer.querySelectorAll(selector);
        console.log(`Selector ${selector} found ${foundElements.length} elements`);

        // Filter to ensure we only get messages from the active chat area
        messageElements = Array.from(foundElements).filter(el => this.isMessageInActiveChat(el));
        console.log(`After filtering for active chat: ${messageElements.length} elements`);

        if (messageElements.length > 0) break;
      }

      // If no messages found with specific selectors, try to find any text elements
      if (messageElements.length === 0) {
        console.log('No messages found with standard selectors, trying fallback...');

        // Look for any elements that might contain message text
        const fallbackSelectors = [
          'span.selectable-text',
          'div[role="row"]',
          'div[data-id*="@"]',
          '.copyable-text span',
          '[data-testid*="conversation"] div'
        ];

        for (const selector of fallbackSelectors) {
          const elements = messagesContainer.querySelectorAll(selector);
          if (elements.length > 0) {
            console.log(`Fallback selector ${selector} found ${elements.length} elements`);
            // Filter elements that actually contain text and are in active chat
            messageElements = Array.from(elements).filter(el =>
              el.textContent &&
              el.textContent.trim().length > 0 &&
              this.isMessageInActiveChat(el)
            );
            if (messageElements.length > 0) break;
          }
        }
      }

      if (messageElements.length === 0) {
        // Last resort: try to extract any text from the container
        const allTextElements = messagesContainer.querySelectorAll('*');
        const textElements = Array.from(allTextElements).filter(el => {
          const text = el.textContent?.trim();
          return text && text.length > 0 && text.length < 1000 &&
                 !el.querySelector('*') && // No child elements
                 el.offsetParent !== null; // Visible
        });

        if (textElements.length > 0) {
          console.log(`Found ${textElements.length} text elements as fallback`);
          messageElements = textElements.slice(0, 50); // Limit to prevent too many
        } else {
          throw new Error('No messages found in the current chat. Please make sure the chat is open and has messages.');
        }
      }

      const messages = [];
      messageElements.forEach((msgElement, index) => {
        const messageData = this.extractMessageData(msgElement, contactName, contactNumber);
        if (messageData) {
          // Apply date filter if enabled
          if (dateFilter && dateFilter.enabled) {
            if (this.isMessageInDateRange(messageData, dateFilter)) {
              messages.push(messageData);
            }
          } else {
            messages.push(messageData);
          }
        }
      });

      // Log filtering results
      if (dateFilter && dateFilter.enabled) {
        console.log(`Date filter applied: ${dateFilter.startDate} to ${dateFilter.endDate}`);
        console.log(`Filtered messages: ${messages.length} out of ${messageElements.length} total`);
      }

      return {
        contact: contactName,
        phoneNumber: contactNumber,
        messages: messages
      };
    } catch (error) {
      console.error('Error extracting chat data:', error);
      throw error;
    }
  }

  // Extract contact name from header
  extractContactName(headerElement) {
    console.log('Extracting contact name from header:', headerElement);

    if (!headerElement) {
      console.log('No header element found');
      return this.getContactNameFromAlternativeSources();
    }

    const nameSelectors = [
      '[data-testid="conversation-info-header-chat-title"]',
      '[data-testid="conversation-title"]',
      'span[title]',
      'h1',
      '.chat-title',
      '[data-testid="conversation-info-header"] span',
      'header span[title]',
      'header h1',
      '[role="button"] span[title]',
      'span[dir="auto"]',
      'div[role="button"] span',
      'span:not([data-testid])',
      'div > span'
    ];

    for (const selector of nameSelectors) {
      const nameElements = headerElement.querySelectorAll(selector);
      for (const nameElement of nameElements) {
        if (nameElement && nameElement.textContent && nameElement.textContent.trim()) {
          const name = nameElement.textContent.trim();
          // Skip if it's just a phone number, time, or common UI text
          if (name &&
              name !== '' &&
              !name.match(/^\+?[\d\s\-\(\)]+$/) &&
              !name.match(/\d{1,2}:\d{2}/) &&
              !name.includes('WhatsApp') &&
              !name.includes('search') &&
              !name.includes('refresh') &&
              !name.includes('Search') &&
              !name.includes('Menu') &&
              !name.includes('Back') &&
              name.length > 1 &&
              name.length < 50) {
            console.log(`Found contact name: ${name} using selector: ${selector}`);
            return name;
          }
        }
      }
    }

    return this.getContactNameFromAlternativeSources();
  }

  // Get contact name from alternative sources
  getContactNameFromAlternativeSources() {
    // Try to get from document title
    const title = document.title;
    if (title && title !== 'WhatsApp') {
      const contactFromTitle = title.replace('WhatsApp', '').replace(/[^\w\s]/g, '').trim();
      if (contactFromTitle &&
          contactFromTitle.length > 1 &&
          !contactFromTitle.includes('search') &&
          !contactFromTitle.includes('refresh')) {
        console.log(`Found contact name from title: ${contactFromTitle}`);
        return contactFromTitle;
      }
    }

    // Try to get from URL
    const url = window.location.href;
    const urlMatch = url.match(/\/chat\/([^\/]+)/);
    if (urlMatch) {
      const chatId = decodeURIComponent(urlMatch[1]);
      if (chatId && !chatId.match(/^\+?[\d\s\-\(\)]+$/)) {
        console.log(`Found contact name from URL: ${chatId}`);
        return chatId;
      }
    }

    // Try to find contact name in the main chat area specifically
    const mainArea = document.querySelector('#main, [data-testid="main"]');
    if (mainArea) {
      const possibleNameElements = mainArea.querySelectorAll('span[dir="auto"], h1, [role="button"] span');
      for (const element of possibleNameElements) {
        const text = element.textContent?.trim();
        if (text &&
            text.length > 1 &&
            text.length < 50 &&
            !text.match(/^\+?[\d\s\-\(\)]+$/) &&
            !text.match(/\d{1,2}:\d{2}/) &&
            !text.includes('WhatsApp') &&
            !text.includes('search') &&
            !text.includes('refresh') &&
            !text.includes('Search') &&
            !text.includes('Menu') &&
            !text.includes('Back') &&
            !text.includes('Type') &&
            !text.includes('Emoji')) {

          // Additional check: make sure it's not in the sidebar
          const isInSidebar = element.closest('#pane-side, [data-testid="chat-list"]');
          if (!isInSidebar) {
            console.log(`Found potential contact name: ${text}`);
            return text;
          }
        }
      }
    }

    console.log('Could not find contact name, returning Unknown');
    return 'Unknown';
  }

  // Get contact name from phone number (if it's a saved contact)
  getContactNameFromPhoneNumber(phoneNumber) {
    if (!phoneNumber || phoneNumber === 'Unknown') return null;

    // This is a placeholder - in reality, WhatsApp Web doesn't expose contact names
    // from phone numbers directly, but we can try to find it in the page
    console.log(`Trying to find contact name for phone: ${phoneNumber}`);
    return null;
  }

  // Get contact name from page title
  getContactNameFromPageTitle() {
    const title = document.title;
    if (title && title !== 'WhatsApp') {
      // Clean the title to extract just the contact name
      let cleanTitle = title.replace('WhatsApp', '').replace(/[^\w\s]/g, '').trim();

      if (cleanTitle &&
          cleanTitle.length > 1 &&
          cleanTitle.length < 50 &&
          !cleanTitle.includes('search') &&
          !cleanTitle.includes('refresh') &&
          !cleanTitle.includes('Web')) {
        console.log(`Found contact name from page title: ${cleanTitle}`);
        return cleanTitle;
      }
    }
    return null;
  }

  // Get contact name from chat list (find the selected/active chat)
  getContactNameFromChatList() {
    const chatList = document.querySelector('[data-testid="chat-list"]');
    if (!chatList) return null;

    // Look for the active/selected chat in the list
    const activeChatSelectors = [
      '.active',
      '[aria-selected="true"]',
      '[data-testid="cell-frame-container"][style*="background"]',
      '[data-testid="cell-frame-container"].selected'
    ];

    for (const selector of activeChatSelectors) {
      const activeChat = chatList.querySelector(selector);
      if (activeChat) {
        const nameElement = activeChat.querySelector('[data-testid="conversation-info-header"] span, span[title]');
        if (nameElement) {
          const name = nameElement.textContent?.trim();
          if (name &&
              name.length > 1 &&
              name.length < 50 &&
              !name.includes('search') &&
              !name.includes('refresh') &&
              !name.match(/^\+?[\d\s\-\(\)]+$/)) {
            console.log(`Found contact name from chat list: ${name}`);
            return name;
          }
        }
      }
    }

    return null;
  }

  // Validate that the phone number belongs to the currently active chat
  validatePhoneNumberForActiveChat(phoneNumber, contactName) {
    if (!phoneNumber || phoneNumber === 'Unknown') {
      console.log('No phone number to validate');
      return phoneNumber;
    }

    // Check if the URL contains this phone number (most reliable validation)
    const url = window.location.href;
    if (url.includes(phoneNumber) || url.includes(phoneNumber.replace('+', ''))) {
      console.log(`Phone number ${phoneNumber} validated against URL`);
      return phoneNumber;
    }

    // If URL doesn't contain the phone number, try to get it from URL directly
    const urlPhoneMatch = url.match(/\/chat\/(\+?[\d]+)/);
    if (urlPhoneMatch && urlPhoneMatch[1]) {
      const urlPhone = urlPhoneMatch[1];
      if (urlPhone.length >= 10) {
        console.log(`Using phone number from URL instead: ${urlPhone} (was: ${phoneNumber})`);
        return urlPhone;
      }
    }

    // If we have a contact name, try to find the phone number specifically for this contact
    if (contactName && contactName !== 'Unknown' && !contactName.includes('search')) {
      const mainArea = document.querySelector('#main, [data-testid="main"]');
      if (mainArea) {
        // Look for phone numbers only in the active chat area
        const elements = mainArea.querySelectorAll('span[title], div[title]');
        for (const element of elements) {
          const title = element.getAttribute('title') || '';
          if (title.includes(phoneNumber) || phoneNumber.includes(title.replace(/\D/g, ''))) {
            console.log(`Phone number ${phoneNumber} validated against element title`);
            return phoneNumber;
          }
        }
      }
    }

    console.log(`Could not validate phone number ${phoneNumber} for active chat, keeping it anyway`);
    return phoneNumber;
  }

  // Check if the messages container is for the active chat
  isActiveChat(messagesContainer) {
    if (!messagesContainer) return false;

    // Check if it's in the main chat area (not sidebar or other areas)
    const mainArea = messagesContainer.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's visible and has a reasonable size
    const rect = messagesContainer.getBoundingClientRect();
    if (rect.width < 200 || rect.height < 100) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(messagesContainer);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    console.log('Confirmed this is the active chat container');
    return true;
  }

  // Check if the header element is for the active chat
  isInActiveChat(headerElement) {
    if (!headerElement) return false;

    // Check if it's in the main chat area
    const mainArea = headerElement.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's visible
    const rect = headerElement.getBoundingClientRect();
    if (rect.width < 100 || rect.height < 20) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(headerElement);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    console.log('Confirmed this is the active chat header');
    return true;
  }

  // Check if a message element is in the active chat
  isMessageInActiveChat(messageElement) {
    if (!messageElement) return false;

    // Check if it's in the main chat area (not in sidebar or other chats)
    const mainArea = messageElement.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's not in the chat list sidebar
    const chatList = messageElement.closest('[data-testid="chat-list"], #pane-side');
    if (chatList) return false;

    // Check if it's visible
    const rect = messageElement.getBoundingClientRect();
    if (rect.width < 50 || rect.height < 10) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(messageElement);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    return true;
  }

  // Check if a message is within the specified date range
  isMessageInDateRange(messageData, dateFilter) {
    if (!dateFilter || !dateFilter.enabled || !messageData.timestamp) {
      return true;
    }

    try {
      // Parse the message timestamp
      const messageDate = this.parseMessageTimestamp(messageData.timestamp);
      if (!messageDate) {
        console.log(`Could not parse timestamp: ${messageData.timestamp}`);
        return true; // Include messages with unparseable timestamps
      }

      // Parse filter dates
      const startDate = new Date(dateFilter.startDate + 'T00:00:00');
      const endDate = new Date(dateFilter.endDate + 'T23:59:59');

      // Check if message date is within range
      const isInRange = messageDate >= startDate && messageDate <= endDate;

      if (!isInRange) {
        console.log(`Message filtered out: ${messageData.timestamp} not in range ${dateFilter.startDate} to ${dateFilter.endDate}`);
      }

      return isInRange;
    } catch (error) {
      console.error('Error filtering message by date:', error);
      return true; // Include message if there's an error
    }
  }

  // Parse WhatsApp timestamp to Date object
  parseMessageTimestamp(timestamp) {
    if (!timestamp) return null;

    try {
      // Handle different timestamp formats
      const today = new Date();
      const currentYear = today.getFullYear();

      // Format: "10:30 AM" or "14:30" (today)
      if (timestamp.match(/^\d{1,2}:\d{2}(\s?(AM|PM))?$/i)) {
        const timeStr = timestamp.toLowerCase();
        let [time, period] = timeStr.split(/\s+/);
        let [hours, minutes] = time.split(':').map(Number);

        if (period === 'pm' && hours !== 12) hours += 12;
        if (period === 'am' && hours === 12) hours = 0;

        const messageDate = new Date(today);
        messageDate.setHours(hours, minutes, 0, 0);
        return messageDate;
      }

      // Format: "Yesterday 10:30 AM"
      if (timestamp.toLowerCase().includes('yesterday')) {
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);

        const timeMatch = timestamp.match(/(\d{1,2}:\d{2}(\s?(AM|PM))?)/i);
        if (timeMatch) {
          const timeStr = timeMatch[1].toLowerCase();
          let [time, period] = timeStr.split(/\s+/);
          let [hours, minutes] = time.split(':').map(Number);

          if (period === 'pm' && hours !== 12) hours += 12;
          if (period === 'am' && hours === 12) hours = 0;

          yesterday.setHours(hours, minutes, 0, 0);
          return yesterday;
        }
        return yesterday;
      }

      // Format: "12/25/2023, 10:30 AM" or similar date formats
      const dateTimeMatch = timestamp.match(/(\d{1,2}\/\d{1,2}\/\d{4}),?\s*(\d{1,2}:\d{2}(\s?(AM|PM))?)/i);
      if (dateTimeMatch) {
        const dateStr = dateTimeMatch[1];
        const timeStr = dateTimeMatch[2].toLowerCase();

        const [month, day, year] = dateStr.split('/').map(Number);
        let [time, period] = timeStr.split(/\s+/);
        let [hours, minutes] = time.split(':').map(Number);

        if (period === 'pm' && hours !== 12) hours += 12;
        if (period === 'am' && hours === 12) hours = 0;

        return new Date(year, month - 1, day, hours, minutes);
      }

      // Try to parse as a standard date
      const parsedDate = new Date(timestamp);
      if (!isNaN(parsedDate.getTime())) {
        return parsedDate;
      }

      console.log(`Could not parse timestamp format: ${timestamp}`);
      return null;
    } catch (error) {
      console.error(`Error parsing timestamp "${timestamp}":`, error);
      return null;
    }
  }

  // Extract individual message data
  extractMessageData(messageElement, contactName, contactNumber) {
    try {
      // Get message text with multiple selectors, excluding timestamp
      const textSelectors = [
        '[data-testid="msg-text"]',
        '.copyable-text',
        '.selectable-text',
        'span.selectable-text'
      ];

      let messageText = '';
      for (const selector of textSelectors) {
        const textElement = messageElement.querySelector(selector);
        if (textElement) {
          let rawText = textElement.textContent || textElement.innerText || '';

          // Remove timestamp from message text if it's included
          // Common patterns: "message text10:30 AM" or "message text1:30 PM"
          messageText = rawText.replace(/\d{1,2}:\d{2}\s?(AM|PM)?$/i, '').trim();

          // If the entire text was just a timestamp, keep the original
          if (!messageText && rawText) {
            messageText = rawText;
          }

          console.log(`Extracted message text: "${messageText}" from raw: "${rawText}"`);
          break;
        }
      }

      // Get timestamp with multiple selectors
      const timeSelectors = [
        '[data-testid="msg-meta"] span[title]',
        '.msg-time span[title]',
        'span[data-testid="msg-time"]',
        '.message-datetime',
        '[data-testid="msg-meta"] span',
        '.copyable-text[data-testid="msg-meta"] span',
        'span[dir="auto"][title]',
        '.msg-time',
        '[data-testid="msg-meta"]'
      ];

      let timestamp = '';
      for (const selector of timeSelectors) {
        const timeElement = messageElement.querySelector(selector);
        if (timeElement) {
          // Try title attribute first, then text content
          timestamp = timeElement.getAttribute('title') ||
                     timeElement.getAttribute('data-title') ||
                     timeElement.textContent ||
                     timeElement.innerText || '';

          if (timestamp && timestamp.trim()) {
            console.log(`Found timestamp: ${timestamp} using selector: ${selector}`);
            timestamp = timestamp.trim();
            break;
          }
        }
      }

      // If still no timestamp, try to find any time-like text in the message element
      if (!timestamp) {
        const allSpans = messageElement.querySelectorAll('span');
        for (const span of allSpans) {
          const text = span.textContent || span.innerText || '';
          // Look for time patterns like "10:30 AM" or "14:30"
          if (text.match(/\d{1,2}:\d{2}(\s?(AM|PM))?/i)) {
            timestamp = text.trim();
            console.log(`Found timestamp from pattern: ${timestamp}`);
            break;
          }
        }
      }

      // Check if message is outgoing or incoming with better detection
      const isOutgoing = this.detectMessageDirection(messageElement);

      // Get sender info - try to extract actual sender name for group chats
      const sender = this.extractSenderName(messageElement, contactName, isOutgoing);

      // Check for attachments
      const attachments = this.extractAttachments(messageElement);

      // Get message status (for outgoing messages) with improved detection
      const messageStatus = this.extractMessageStatus(messageElement, isOutgoing);

      // Debug logging
      console.log('Message extraction debug:', {
        contact: contactName,
        phoneNumber: contactNumber,
        sender: sender,
        messageText: messageText.substring(0, 50) + '...',
        timestamp: timestamp,
        isOutgoing: isOutgoing,
        status: messageStatus,
        attachmentCount: attachments.length
      });

      return {
        contact: contactName,
        phoneNumber: contactNumber,
        sender: sender,
        message: messageText,
        timestamp: timestamp,
        isOutgoing: isOutgoing,
        status: messageStatus,
        attachments: attachments,
        messageType: attachments.length > 0 ? 'media' : 'text'
      };
    } catch (error) {
      console.error('Error extracting message data:', error);
      return null;
    }
  }

  // Extract attachment information
  extractAttachments(messageElement) {
    const attachments = [];

    // Check for images
    const images = messageElement.querySelectorAll('img[src*="blob:"]');
    images.forEach(img => {
      attachments.push({
        type: 'image',
        src: img.src,
        alt: img.alt || 'Image'
      });
    });

    // Check for documents
    const documents = messageElement.querySelectorAll('[data-testid="msg-document"]');
    documents.forEach(doc => {
      const fileName = doc.querySelector('[data-testid="document-name"]')?.textContent || 'Document';
      const fileSize = doc.querySelector('[data-testid="document-size"]')?.textContent || '';
      attachments.push({
        type: 'document',
        fileName: fileName,
        fileSize: fileSize
      });
    });

    // Check for audio messages
    const audioElements = messageElement.querySelectorAll('[data-testid="msg-audio"]');
    audioElements.forEach(audio => {
      const duration = audio.querySelector('[data-testid="audio-duration"]')?.textContent || '';
      attachments.push({
        type: 'audio',
        duration: duration
      });
    });

    // Check for videos
    const videos = messageElement.querySelectorAll('video');
    videos.forEach(video => {
      attachments.push({
        type: 'video',
        src: video.src,
        duration: video.duration || 'Unknown'
      });
    });

    return attachments;
  }

  // Detect message direction (incoming/outgoing)
  detectMessageDirection(messageElement) {
    // Multiple ways to detect outgoing messages
    const outgoingIndicators = [
      () => messageElement.classList.contains('message-out'),
      () => messageElement.querySelector('[data-testid="msg-meta-outgoing"]'),
      () => messageElement.closest('.message-out'),
      () => messageElement.querySelector('[data-testid="tail-out"]'),
      () => messageElement.querySelector('.tail-container-out'),
      () => {
        // Check if message is on the right side (outgoing)
        const rect = messageElement.getBoundingClientRect();
        const parentRect = messageElement.parentElement?.getBoundingClientRect();
        if (rect && parentRect) {
          const centerX = rect.left + rect.width / 2;
          const parentCenterX = parentRect.left + parentRect.width / 2;
          return centerX > parentCenterX;
        }
        return false;
      }
    ];

    for (const indicator of outgoingIndicators) {
      try {
        if (indicator()) {
          return true;
        }
      } catch (e) {
        // Continue to next indicator
      }
    }

    return false;
  }

  // Extract sender name (especially useful for group chats)
  extractSenderName(messageElement, contactName, isOutgoing) {
    if (isOutgoing) {
      return 'You';
    }

    // For incoming messages in individual chats, use the contact name
    // For group chats, try to find the actual sender name

    // First check if this is a group chat by looking for sender names in messages
    const senderSelectors = [
      '[data-testid="msg-meta"] span[dir="auto"]',
      '.copyable-text[data-testid="msg-meta"] span',
      '[data-testid="msg-meta"] .copyable-text',
      'span[dir="auto"][style*="color"]',
      '.message-author',
      '[data-testid="sender-name"]'
    ];

    for (const selector of senderSelectors) {
      const senderElement = messageElement.querySelector(selector);
      if (senderElement) {
        const senderText = senderElement.textContent || senderElement.innerText || '';
        if (senderText &&
            senderText.trim() &&
            !senderText.match(/\d{1,2}:\d{2}/) &&
            !senderText.includes('search') &&
            !senderText.includes('refresh') &&
            senderText.length > 1 &&
            senderText.length < 50) {
          console.log(`Found sender: ${senderText.trim()}`);
          return senderText.trim();
        }
      }
    }

    // If no specific sender found and we have a valid contact name, use it
    if (contactName &&
        contactName !== 'Unknown' &&
        contactName !== 'search-refreshed' &&
        !contactName.includes('search') &&
        !contactName.includes('refresh')) {
      return contactName;
    }

    // Last resort: try to get a clean contact name from the header again
    const cleanContactName = this.getCleanContactName();
    if (cleanContactName && cleanContactName !== 'Unknown') {
      return cleanContactName;
    }

    return 'Unknown Contact';
  }

  // Get a clean contact name, avoiding UI text
  getCleanContactName() {
    const mainArea = document.querySelector('#main, [data-testid="main"]');
    if (!mainArea) return 'Unknown';

    // Look specifically in the header area for the contact name
    const headerArea = mainArea.querySelector('header, [data-testid="conversation-header"]');
    if (headerArea) {
      const nameElements = headerArea.querySelectorAll('span, h1, div');
      for (const element of nameElements) {
        const text = element.textContent?.trim();
        if (text &&
            text.length > 1 &&
            text.length < 50 &&
            !text.match(/^\+?[\d\s\-\(\)]+$/) &&
            !text.match(/\d{1,2}:\d{2}/) &&
            !text.includes('WhatsApp') &&
            !text.includes('search') &&
            !text.includes('refresh') &&
            !text.includes('Search') &&
            !text.includes('Menu') &&
            !text.includes('Back') &&
            !text.includes('Type') &&
            !text.includes('Emoji') &&
            !text.includes('online') &&
            !text.includes('last seen')) {

          // Make sure this element is visible and not a UI element
          const rect = element.getBoundingClientRect();
          if (rect.width > 50 && rect.height > 10) {
            console.log(`Found clean contact name: ${text}`);
            return text;
          }
        }
      }
    }

    return 'Unknown';
  }

  // Extract message status with better detection
  extractMessageStatus(messageElement, isOutgoing) {
    if (!isOutgoing) {
      return 'received';
    }

    // Look for status indicators
    const statusSelectors = [
      '[data-testid="msg-dblcheck"]',
      '[data-testid="msg-check"]',
      '[data-testid="msg-time"] + span',
      '.msg-check',
      '.msg-dblcheck'
    ];

    for (const selector of statusSelectors) {
      const statusElement = messageElement.querySelector(selector);
      if (statusElement) {
        const testId = statusElement.getAttribute('data-testid');

        if (testId === 'msg-dblcheck') {
          // Double check mark - delivered or read
          if (statusElement.classList.contains('read') ||
              statusElement.style.color === 'rgb(83, 188, 254)' ||
              statusElement.querySelector('[fill="#53bcfe"]')) {
            return 'read';
          }
          return 'delivered';
        } else if (testId === 'msg-check') {
          return 'sent';
        }
      }
    }

    return 'sent';
  }

  // Extract phone number from contact info for the ACTIVE chat
  extractPhoneNumber(headerElement) {
    console.log('Extracting phone number from ACTIVE chat header:', headerElement);

    // Try to extract from URL first, but make sure it's for the current active chat
    const url = window.location.href;
    console.log('Current URL:', url);

    // WhatsApp Web URL patterns - these should be for the active chat
    const urlPatterns = [
      /\/chat\/(\+?[\d]+)@c\.us/,  // Individual chat pattern
      /\/chat\/(\+?[\d]+)@/,       // More specific pattern for individual chats
      /\/chat\/(\+?[\d]{10,15})/,  // General chat pattern
      /phone=(\+?[\d]+)/           // Phone parameter
    ];

    for (const pattern of urlPatterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        const phone = match[1];
        if (phone.length >= 10) {
          console.log(`Found phone number from ACTIVE chat URL: ${phone}`);
          return phone;
        }
      }
    }

    // Focus on the ACTIVE chat header only
    if (!headerElement) {
      console.log('No header element for phone extraction, trying active chat area');
      return this.getPhoneFromActiveChatArea();
    }

    // Try to find phone number in the ACTIVE chat header elements only
    const phoneSelectors = [
      'span[title*="+"]',
      'span[dir="auto"]',
      '[data-testid="conversation-info-header"] span',
      'span[title]',
      'div[title]'
    ];

    for (const selector of phoneSelectors) {
      const phoneElements = headerElement.querySelectorAll(selector);
      for (let element of phoneElements) {
        // Make sure this element is in the active chat area
        if (!this.isInActiveChat(element)) continue;

        const sources = [
          element.getAttribute('title'),
          element.getAttribute('data-title'),
          element.textContent,
          element.innerText
        ];

        for (const text of sources) {
          if (text && /^\+?[\d\s\-\(\)]{10,}$/.test(text.trim())) {
            const cleanPhone = text.trim().replace(/[\s\-\(\)]/g, '');
            if (cleanPhone.length >= 10) {
              console.log(`Found phone number from ACTIVE chat header: ${cleanPhone} using selector: ${selector}`);
              return cleanPhone;
            }
          }
        }
      }
    }

    return this.getPhoneFromActiveChatArea();
  }

  // Get phone number specifically from the ACTIVE chat area
  getPhoneFromActiveChatArea() {
    console.log('Searching for phone number in ACTIVE chat area only');

    // Focus only on the main chat area, not the entire page
    const mainArea = document.querySelector('#main, [data-testid="main"]');
    if (!mainArea) {
      console.log('No main chat area found');
      return 'Unknown';
    }

    // Try to find phone numbers only in the active chat area
    const elements = mainArea.querySelectorAll('span, div');
    for (const element of elements) {
      // Skip if element is in sidebar or chat list
      if (element.closest('#pane-side, [data-testid="chat-list"]')) continue;

      const sources = [
        element.getAttribute('title'),
        element.getAttribute('data-title'),
        element.textContent,
        element.innerText
      ];

      for (const text of sources) {
        if (text && /^\+?[\d\s\-\(\)]{10,}$/.test(text.trim())) {
          const cleanPhone = text.trim().replace(/[\s\-\(\)]/g, '');
          if (cleanPhone.length >= 10 && cleanPhone.length <= 15) {
            // Additional validation: make sure this is visible and in the right area
            const rect = element.getBoundingClientRect();
            if (rect.width > 0 && rect.height > 0) {
              console.log(`Found phone number from ACTIVE chat area: ${cleanPhone}`);
              return cleanPhone;
            }
          }
        }
      }
    }

    // Last resort: try to extract from the current chat URL more aggressively
    const url = window.location.href;
    const phoneMatch = url.match(/(\+?[\d]{10,15})/);
    if (phoneMatch) {
      console.log(`Found phone number from URL as last resort: ${phoneMatch[1]}`);
      return phoneMatch[1];
    }

    console.log('Could not find phone number for active chat');
    return 'Unknown';
  }

  // Get phone number from alternative sources (DEPRECATED - use getPhoneFromActiveChatArea)
  getPhoneFromAlternativeSources() {
    return this.getPhoneFromActiveChatArea();
  }

  // Get all contacts from chat list
  async extractAllContacts() {
    try {
      console.log('Starting contact extraction...');

      // Wait for chat list to be available
      const chatListSelectors = [
        '[data-testid="chat-list"]',
        '#pane-side [data-testid="chat-list"]',
        '[data-testid="side"] [data-testid="chat-list"]',
        '.chat-list'
      ];

      let chatList = null;
      for (const selector of chatListSelectors) {
        try {
          await this.waitForElement(selector, 3000);
          chatList = document.querySelector(selector);
          if (chatList) {
            console.log(`Found chat list with selector: ${selector}`);
            break;
          }
        } catch (e) {
          console.log(`Chat list selector ${selector} not found, trying next...`);
        }
      }

      if (!chatList) {
        throw new Error('Chat list not found. Please make sure WhatsApp Web is fully loaded.');
      }

      // Multiple selectors for chat elements
      const chatElementSelectors = [
        '[data-testid="cell-frame-container"]',
        '[data-testid="chat"]',
        '.chat-item',
        '[role="listitem"]'
      ];

      let chatElements = [];
      for (const selector of chatElementSelectors) {
        chatElements = chatList.querySelectorAll(selector);
        console.log(`Selector ${selector} found ${chatElements.length} chat elements`);
        if (chatElements.length > 0) break;
      }

      if (chatElements.length === 0) {
        throw new Error('No chat elements found in the chat list.');
      }

      const contacts = [];
      chatElements.forEach((chatElement, index) => {
        try {
          const contactData = this.extractContactFromElement(chatElement, index);
          if (contactData) {
            contacts.push(contactData);
          }
        } catch (error) {
          console.error(`Error extracting contact ${index}:`, error);
        }
      });

      console.log(`Successfully extracted ${contacts.length} contacts`);
      return contacts;
    } catch (error) {
      console.error('Error extracting contacts:', error);
      throw error;
    }
  }

  // Extract contact information from a chat element
  extractContactFromElement(chatElement, index) {
    // Multiple selectors for contact name
    const nameSelectors = [
      '[data-testid="conversation-info-header"] span[title]',
      'span[title]',
      '[data-testid="conversation-title"]',
      'span[dir="auto"]',
      'h3',
      '.chat-title'
    ];

    let contactName = '';
    for (const selector of nameSelectors) {
      const nameElement = chatElement.querySelector(selector);
      if (nameElement && nameElement.textContent && nameElement.textContent.trim()) {
        contactName = nameElement.textContent.trim();
        if (contactName.length > 0 && contactName.length < 100) {
          break;
        }
      }
    }

    // Multiple selectors for last message
    const messageSelectors = [
      '[data-testid="last-msg-text"]',
      '.last-message',
      'span[title]',
      'span[dir="ltr"]'
    ];

    let lastMessage = '';
    for (const selector of messageSelectors) {
      const messageElement = chatElement.querySelector(selector);
      if (messageElement && messageElement.textContent) {
        lastMessage = messageElement.textContent.trim();
        if (lastMessage && lastMessage !== contactName) {
          break;
        }
      }
    }

    // Multiple selectors for time
    const timeSelectors = [
      '[data-testid="msg-meta-time"]',
      '.msg-time',
      'span[data-testid="time"]',
      'time'
    ];

    let lastMessageTime = '';
    for (const selector of timeSelectors) {
      const timeElement = chatElement.querySelector(selector);
      if (timeElement && timeElement.textContent) {
        lastMessageTime = timeElement.textContent.trim();
        break;
      }
    }

    // Try to extract phone number from the chat element
    let phoneNumber = 'Unknown';
    const phoneElements = chatElement.querySelectorAll('span, div');
    for (const element of phoneElements) {
      const text = element.textContent || element.getAttribute('title') || '';
      if (text && /^\+?[\d\s\-\(\)]{10,}$/.test(text.trim())) {
        const cleanPhone = text.trim().replace(/[\s\-\(\)]/g, '');
        if (cleanPhone.length >= 10 && cleanPhone.length <= 15) {
          phoneNumber = cleanPhone;
          break;
        }
      }
    }

    if (contactName) {
      console.log(`Extracted contact ${index}: ${contactName}`);
      return {
        name: contactName,
        lastMessage: lastMessage || 'No recent message',
        lastMessageTime: lastMessageTime || 'Unknown',
        phoneNumber: phoneNumber
      };
    }

    return null;
  }

  // Get page structure for debugging
  getPageStructure() {
    const structure = {
      hasApp: false,
      hasQR: false,
      hasChat: false,
      messageCount: 0,
      contactName: 'Unknown',
      selectors: []
    };

    // Check for app container
    const appSelectors = [
      '[data-testid="app-wrapper-web"]',
      '.app-wrapper-web',
      '#app',
      '[data-testid="main"]'
    ];

    for (const selector of appSelectors) {
      if (document.querySelector(selector)) {
        structure.hasApp = true;
        structure.selectors.push(`App: ${selector}`);
        break;
      }
    }

    // Check for QR code
    const qrCode = document.querySelector('[data-testid="qr-code"]');
    structure.hasQR = qrCode && qrCode.offsetParent !== null;

    // Check for chat container
    const messageSelectors = [
      '[data-testid="conversation-panel-messages"]',
      '[data-testid="main"] [role="application"]',
      '#main [data-testid="conversation-panel-messages"]',
      '.copyable-area [role="application"]',
      '#main .copyable-area',
      '[data-testid="main"] .copyable-area'
    ];

    let messagesContainer = null;
    for (const selector of messageSelectors) {
      messagesContainer = document.querySelector(selector);
      if (messagesContainer) {
        structure.hasChat = true;
        structure.selectors.push(`Messages: ${selector}`);
        break;
      }
    }

    // Count messages if chat is open
    if (messagesContainer) {
      const msgSelectors = [
        '[data-testid="msg-container"]',
        '[data-testid="msg"]',
        '.message-in, .message-out',
        'div[data-id]'
      ];

      for (const selector of msgSelectors) {
        const messages = messagesContainer.querySelectorAll(selector);
        if (messages.length > 0) {
          structure.messageCount = messages.length;
          structure.selectors.push(`Msg elements: ${selector} (${messages.length})`);
          break;
        }
      }
    }

    // Get contact name and phone number
    const headerSelectors = [
      '[data-testid="conversation-header"]',
      'header[data-testid="conversation-header"]',
      '[data-testid="chat-header"]',
      'header',
      '[role="banner"]'
    ];

    for (const selector of headerSelectors) {
      const header = document.querySelector(selector);
      if (header) {
        structure.contactName = this.extractContactName(header);
        structure.phoneNumber = this.extractPhoneNumber(header);
        structure.selectors.push(`Header: ${selector}`);
        break;
      }
    }

    // Add current URL for debugging
    structure.currentUrl = window.location.href;
    structure.pageTitle = document.title;

    return structure;
  }
}

// Initialize extractor
const extractor = new WhatsAppDataExtractor();

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handle ping to check if content script is loaded
  if (request.action === 'ping') {
    sendResponse({ success: true, message: 'Content script is ready' });
    return;
  }

  if (request.action === 'getPageStructure') {
    try {
      const structure = extractor.getPageStructure();
      sendResponse({ success: true, data: structure });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
    return;
  }

  if (request.action === 'extractCurrentChat') {
    extractor.extractCurrentChatData(request.dateFilter)
      .then(data => sendResponse({ success: true, data: data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }

  if (request.action === 'extractAllContacts') {
    extractor.extractAllContacts()
      .then(data => sendResponse({ success: true, data: data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

console.log('WhatsApp Web Data Extractor loaded');
