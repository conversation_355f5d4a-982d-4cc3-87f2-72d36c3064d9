// WhatsApp Web Data Extractor Content Script

class WhatsAppDataExtractor {
  constructor() {
    this.chatData = [];
    this.contactData = [];
    this.attachmentData = [];
  }

  // Wait for element to be available
  waitForElement(selector, timeout = 10000) {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  }

  // Wait for WhatsApp to fully load
  async waitForWhatsAppToLoad() {
    // Check if we're on WhatsApp Web
    if (!window.location.href.includes('web.whatsapp.com')) {
      throw new Error('Please navigate to web.whatsapp.com first');
    }

    // Wait for the main app to load
    const appSelectors = [
      '[data-testid="app-wrapper-web"]',
      '.app-wrapper-web',
      '#app',
      '[data-testid="main"]'
    ];

    let appLoaded = false;
    for (const selector of appSelectors) {
      if (document.querySelector(selector)) {
        appLoaded = true;
        break;
      }
    }

    if (!appLoaded) {
      throw new Error('WhatsApp Web is not fully loaded. Please wait for the page to load completely.');
    }

    // Check if user is logged in (no QR code visible)
    const qrCode = document.querySelector('[data-testid="qr-code"]');
    if (qrCode && qrCode.offsetParent !== null) {
      throw new Error('Please scan the QR code to log in to WhatsApp Web first.');
    }

    // Wait a moment for the interface to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));
  }

  // Extract current chat messages
  async extractCurrentChatData() {
    try {
      // First, check if we're on the right page and a chat is open
      await this.waitForWhatsAppToLoad();

      // Wait a moment for the chat to be fully loaded
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Find the ACTIVE/CURRENT chat container (not just any chat)
      const activeMessageSelectors = [
        '#main [data-testid="conversation-panel-messages"]',
        '#main [role="application"]',
        '[data-testid="main"] [data-testid="conversation-panel-messages"]',
        '[data-testid="main"] [role="application"]',
        '#main .copyable-area',
        '[data-testid="main"] .copyable-area'
      ];

      let messagesContainer = null;

      // Try each selector specifically for the main/active chat area
      for (const selector of activeMessageSelectors) {
        try {
          console.log(`Trying active chat selector: ${selector}`);
          messagesContainer = document.querySelector(selector);
          if (messagesContainer && this.isActiveChat(messagesContainer)) {
            console.log(`Found ACTIVE messages container with selector: ${selector}`);
            break;
          }
        } catch (e) {
          console.log(`Selector ${selector} failed:`, e);
        }
      }

      // If still not found, try waiting a bit more and check again
      if (!messagesContainer) {
        console.log('Messages container not found immediately, waiting and retrying...');
        await new Promise(resolve => setTimeout(resolve, 2000));

        for (const selector of messageSelectors) {
          messagesContainer = document.querySelector(selector);
          if (messagesContainer) {
            console.log(`Found messages container on retry with selector: ${selector}`);
            break;
          }
        }
      }

      if (!messagesContainer) {
        // Try to find any element that might contain messages
        const fallbackSelectors = [
          '#main',
          '[data-testid="main"]',
          '.copyable-area',
          '[role="main"]'
        ];

        for (const selector of fallbackSelectors) {
          const element = document.querySelector(selector);
          if (element) {
            console.log(`Using fallback container: ${selector}`);
            messagesContainer = element;
            break;
          }
        }
      }

      if (!messagesContainer) {
        throw new Error('No chat is currently open. Please open a chat conversation and try again.');
      }

      // Get chat header for the ACTIVE chat only
      const activeHeaderSelectors = [
        '#main [data-testid="conversation-header"]',
        '#main header[data-testid="conversation-header"]',
        '[data-testid="main"] [data-testid="conversation-header"]',
        '[data-testid="main"] header',
        '#main header',
        '#main [data-testid="chat-header"]'
      ];

      let chatHeader = null;
      for (const selector of activeHeaderSelectors) {
        chatHeader = document.querySelector(selector);
        if (chatHeader && this.isInActiveChat(chatHeader)) {
          console.log(`Found ACTIVE chat header with selector: ${selector}`);
          break;
        }
      }

      let contactName = this.extractContactName(chatHeader);
      const contactNumber = this.extractPhoneNumber(chatHeader);

      // If contact name is still problematic, try to get it from phone number or other sources
      if (!contactName ||
          contactName === 'Unknown' ||
          contactName === 'search-refreshed' ||
          contactName.includes('search') ||
          contactName.includes('refresh')) {

        console.log('Contact name is problematic, trying alternative methods...');
        contactName = this.getContactNameFromPhoneNumber(contactNumber) ||
                     this.getContactNameFromPageTitle() ||
                     this.getContactNameFromChatList() ||
                     'Unknown Contact';
      }

      console.log(`Extracted ACTIVE chat info - Name: ${contactName}, Phone: ${contactNumber}`);

      // Get all message elements with multiple selectors
      const msgSelectors = [
        '[data-testid="msg-container"]',
        '[data-testid="msg"]',
        '.message-in, .message-out',
        '[role="row"]',
        'div[data-id]',
        '.copyable-text',
        '[data-testid*="msg"]'
      ];

      let messageElements = [];
      for (const selector of msgSelectors) {
        const foundElements = messagesContainer.querySelectorAll(selector);
        console.log(`Selector ${selector} found ${foundElements.length} elements`);

        // Filter to ensure we only get messages from the active chat area
        messageElements = Array.from(foundElements).filter(el => this.isMessageInActiveChat(el));
        console.log(`After filtering for active chat: ${messageElements.length} elements`);

        if (messageElements.length > 0) break;
      }

      // If no messages found with specific selectors, try to find any text elements
      if (messageElements.length === 0) {
        console.log('No messages found with standard selectors, trying fallback...');

        // Look for any elements that might contain message text
        const fallbackSelectors = [
          'span.selectable-text',
          'div[role="row"]',
          'div[data-id*="@"]',
          '.copyable-text span',
          '[data-testid*="conversation"] div'
        ];

        for (const selector of fallbackSelectors) {
          const elements = messagesContainer.querySelectorAll(selector);
          if (elements.length > 0) {
            console.log(`Fallback selector ${selector} found ${elements.length} elements`);
            // Filter elements that actually contain text and are in active chat
            messageElements = Array.from(elements).filter(el =>
              el.textContent &&
              el.textContent.trim().length > 0 &&
              this.isMessageInActiveChat(el)
            );
            if (messageElements.length > 0) break;
          }
        }
      }

      if (messageElements.length === 0) {
        // Last resort: try to extract any text from the container
        const allTextElements = messagesContainer.querySelectorAll('*');
        const textElements = Array.from(allTextElements).filter(el => {
          const text = el.textContent?.trim();
          return text && text.length > 0 && text.length < 1000 &&
                 !el.querySelector('*') && // No child elements
                 el.offsetParent !== null; // Visible
        });

        if (textElements.length > 0) {
          console.log(`Found ${textElements.length} text elements as fallback`);
          messageElements = textElements.slice(0, 50); // Limit to prevent too many
        } else {
          throw new Error('No messages found in the current chat. Please make sure the chat is open and has messages.');
        }
      }

      const messages = [];
      messageElements.forEach((msgElement, index) => {
        const messageData = this.extractMessageData(msgElement, contactName, contactNumber);
        if (messageData) {
          messages.push(messageData);
        }
      });

      return {
        contact: contactName,
        phoneNumber: contactNumber,
        messages: messages
      };
    } catch (error) {
      console.error('Error extracting chat data:', error);
      throw error;
    }
  }

  // Extract contact name from header
  extractContactName(headerElement) {
    console.log('Extracting contact name from header:', headerElement);

    if (!headerElement) {
      console.log('No header element found');
      return this.getContactNameFromAlternativeSources();
    }

    const nameSelectors = [
      '[data-testid="conversation-info-header-chat-title"]',
      '[data-testid="conversation-title"]',
      'span[title]',
      'h1',
      '.chat-title',
      '[data-testid="conversation-info-header"] span',
      'header span[title]',
      'header h1',
      '[role="button"] span[title]',
      'span[dir="auto"]',
      'div[role="button"] span',
      'span:not([data-testid])',
      'div > span'
    ];

    for (const selector of nameSelectors) {
      const nameElements = headerElement.querySelectorAll(selector);
      for (const nameElement of nameElements) {
        if (nameElement && nameElement.textContent && nameElement.textContent.trim()) {
          const name = nameElement.textContent.trim();
          // Skip if it's just a phone number, time, or common UI text
          if (name &&
              name !== '' &&
              !name.match(/^\+?[\d\s\-\(\)]+$/) &&
              !name.match(/\d{1,2}:\d{2}/) &&
              !name.includes('WhatsApp') &&
              !name.includes('search') &&
              !name.includes('refresh') &&
              !name.includes('Search') &&
              !name.includes('Menu') &&
              !name.includes('Back') &&
              name.length > 1 &&
              name.length < 50) {
            console.log(`Found contact name: ${name} using selector: ${selector}`);
            return name;
          }
        }
      }
    }

    return this.getContactNameFromAlternativeSources();
  }

  // Get contact name from alternative sources
  getContactNameFromAlternativeSources() {
    // Try to get from document title
    const title = document.title;
    if (title && title !== 'WhatsApp') {
      const contactFromTitle = title.replace('WhatsApp', '').replace(/[^\w\s]/g, '').trim();
      if (contactFromTitle &&
          contactFromTitle.length > 1 &&
          !contactFromTitle.includes('search') &&
          !contactFromTitle.includes('refresh')) {
        console.log(`Found contact name from title: ${contactFromTitle}`);
        return contactFromTitle;
      }
    }

    // Try to get from URL
    const url = window.location.href;
    const urlMatch = url.match(/\/chat\/([^\/]+)/);
    if (urlMatch) {
      const chatId = decodeURIComponent(urlMatch[1]);
      if (chatId && !chatId.match(/^\+?[\d\s\-\(\)]+$/)) {
        console.log(`Found contact name from URL: ${chatId}`);
        return chatId;
      }
    }

    // Try to find contact name in the main chat area specifically
    const mainArea = document.querySelector('#main, [data-testid="main"]');
    if (mainArea) {
      const possibleNameElements = mainArea.querySelectorAll('span[dir="auto"], h1, [role="button"] span');
      for (const element of possibleNameElements) {
        const text = element.textContent?.trim();
        if (text &&
            text.length > 1 &&
            text.length < 50 &&
            !text.match(/^\+?[\d\s\-\(\)]+$/) &&
            !text.match(/\d{1,2}:\d{2}/) &&
            !text.includes('WhatsApp') &&
            !text.includes('search') &&
            !text.includes('refresh') &&
            !text.includes('Search') &&
            !text.includes('Menu') &&
            !text.includes('Back') &&
            !text.includes('Type') &&
            !text.includes('Emoji')) {

          // Additional check: make sure it's not in the sidebar
          const isInSidebar = element.closest('#pane-side, [data-testid="chat-list"]');
          if (!isInSidebar) {
            console.log(`Found potential contact name: ${text}`);
            return text;
          }
        }
      }
    }

    console.log('Could not find contact name, returning Unknown');
    return 'Unknown';
  }

  // Get contact name from phone number (if it's a saved contact)
  getContactNameFromPhoneNumber(phoneNumber) {
    if (!phoneNumber || phoneNumber === 'Unknown') return null;

    // This is a placeholder - in reality, WhatsApp Web doesn't expose contact names
    // from phone numbers directly, but we can try to find it in the page
    console.log(`Trying to find contact name for phone: ${phoneNumber}`);
    return null;
  }

  // Get contact name from page title
  getContactNameFromPageTitle() {
    const title = document.title;
    if (title && title !== 'WhatsApp') {
      // Clean the title to extract just the contact name
      let cleanTitle = title.replace('WhatsApp', '').replace(/[^\w\s]/g, '').trim();

      if (cleanTitle &&
          cleanTitle.length > 1 &&
          cleanTitle.length < 50 &&
          !cleanTitle.includes('search') &&
          !cleanTitle.includes('refresh') &&
          !cleanTitle.includes('Web')) {
        console.log(`Found contact name from page title: ${cleanTitle}`);
        return cleanTitle;
      }
    }
    return null;
  }

  // Get contact name from chat list (find the selected/active chat)
  getContactNameFromChatList() {
    const chatList = document.querySelector('[data-testid="chat-list"]');
    if (!chatList) return null;

    // Look for the active/selected chat in the list
    const activeChatSelectors = [
      '.active',
      '[aria-selected="true"]',
      '[data-testid="cell-frame-container"][style*="background"]',
      '[data-testid="cell-frame-container"].selected'
    ];

    for (const selector of activeChatSelectors) {
      const activeChat = chatList.querySelector(selector);
      if (activeChat) {
        const nameElement = activeChat.querySelector('[data-testid="conversation-info-header"] span, span[title]');
        if (nameElement) {
          const name = nameElement.textContent?.trim();
          if (name &&
              name.length > 1 &&
              name.length < 50 &&
              !name.includes('search') &&
              !name.includes('refresh') &&
              !name.match(/^\+?[\d\s\-\(\)]+$/)) {
            console.log(`Found contact name from chat list: ${name}`);
            return name;
          }
        }
      }
    }

    return null;
  }

  // Check if the messages container is for the active chat
  isActiveChat(messagesContainer) {
    if (!messagesContainer) return false;

    // Check if it's in the main chat area (not sidebar or other areas)
    const mainArea = messagesContainer.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's visible and has a reasonable size
    const rect = messagesContainer.getBoundingClientRect();
    if (rect.width < 200 || rect.height < 100) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(messagesContainer);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    console.log('Confirmed this is the active chat container');
    return true;
  }

  // Check if the header element is for the active chat
  isInActiveChat(headerElement) {
    if (!headerElement) return false;

    // Check if it's in the main chat area
    const mainArea = headerElement.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's visible
    const rect = headerElement.getBoundingClientRect();
    if (rect.width < 100 || rect.height < 20) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(headerElement);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    console.log('Confirmed this is the active chat header');
    return true;
  }

  // Check if a message element is in the active chat
  isMessageInActiveChat(messageElement) {
    if (!messageElement) return false;

    // Check if it's in the main chat area (not in sidebar or other chats)
    const mainArea = messageElement.closest('#main, [data-testid="main"]');
    if (!mainArea) return false;

    // Check if it's not in the chat list sidebar
    const chatList = messageElement.closest('[data-testid="chat-list"], #pane-side');
    if (chatList) return false;

    // Check if it's visible
    const rect = messageElement.getBoundingClientRect();
    if (rect.width < 50 || rect.height < 10) return false;

    // Check if it's not hidden
    const style = window.getComputedStyle(messageElement);
    if (style.display === 'none' || style.visibility === 'hidden') return false;

    return true;
  }

  // Extract individual message data
  extractMessageData(messageElement, contactName, contactNumber) {
    try {
      // Get message text with multiple selectors, excluding timestamp
      const textSelectors = [
        '[data-testid="msg-text"]',
        '.copyable-text',
        '.selectable-text',
        'span.selectable-text'
      ];

      let messageText = '';
      for (const selector of textSelectors) {
        const textElement = messageElement.querySelector(selector);
        if (textElement) {
          let rawText = textElement.textContent || textElement.innerText || '';

          // Remove timestamp from message text if it's included
          // Common patterns: "message text10:30 AM" or "message text1:30 PM"
          messageText = rawText.replace(/\d{1,2}:\d{2}\s?(AM|PM)?$/i, '').trim();

          // If the entire text was just a timestamp, keep the original
          if (!messageText && rawText) {
            messageText = rawText;
          }

          console.log(`Extracted message text: "${messageText}" from raw: "${rawText}"`);
          break;
        }
      }

      // Get timestamp with multiple selectors
      const timeSelectors = [
        '[data-testid="msg-meta"] span[title]',
        '.msg-time span[title]',
        'span[data-testid="msg-time"]',
        '.message-datetime',
        '[data-testid="msg-meta"] span',
        '.copyable-text[data-testid="msg-meta"] span',
        'span[dir="auto"][title]',
        '.msg-time',
        '[data-testid="msg-meta"]'
      ];

      let timestamp = '';
      for (const selector of timeSelectors) {
        const timeElement = messageElement.querySelector(selector);
        if (timeElement) {
          // Try title attribute first, then text content
          timestamp = timeElement.getAttribute('title') ||
                     timeElement.getAttribute('data-title') ||
                     timeElement.textContent ||
                     timeElement.innerText || '';

          if (timestamp && timestamp.trim()) {
            console.log(`Found timestamp: ${timestamp} using selector: ${selector}`);
            timestamp = timestamp.trim();
            break;
          }
        }
      }

      // If still no timestamp, try to find any time-like text in the message element
      if (!timestamp) {
        const allSpans = messageElement.querySelectorAll('span');
        for (const span of allSpans) {
          const text = span.textContent || span.innerText || '';
          // Look for time patterns like "10:30 AM" or "14:30"
          if (text.match(/\d{1,2}:\d{2}(\s?(AM|PM))?/i)) {
            timestamp = text.trim();
            console.log(`Found timestamp from pattern: ${timestamp}`);
            break;
          }
        }
      }

      // Check if message is outgoing or incoming with better detection
      const isOutgoing = this.detectMessageDirection(messageElement);

      // Get sender info - try to extract actual sender name for group chats
      const sender = this.extractSenderName(messageElement, contactName, isOutgoing);

      // Check for attachments
      const attachments = this.extractAttachments(messageElement);

      // Get message status (for outgoing messages) with improved detection
      const messageStatus = this.extractMessageStatus(messageElement, isOutgoing);

      // Debug logging
      console.log('Message extraction debug:', {
        contact: contactName,
        phoneNumber: contactNumber,
        sender: sender,
        messageText: messageText.substring(0, 50) + '...',
        timestamp: timestamp,
        isOutgoing: isOutgoing,
        status: messageStatus,
        attachmentCount: attachments.length
      });

      return {
        contact: contactName,
        phoneNumber: contactNumber,
        sender: sender,
        message: messageText,
        timestamp: timestamp,
        isOutgoing: isOutgoing,
        status: messageStatus,
        attachments: attachments,
        messageType: attachments.length > 0 ? 'media' : 'text'
      };
    } catch (error) {
      console.error('Error extracting message data:', error);
      return null;
    }
  }

  // Extract attachment information
  extractAttachments(messageElement) {
    const attachments = [];

    // Check for images
    const images = messageElement.querySelectorAll('img[src*="blob:"]');
    images.forEach(img => {
      attachments.push({
        type: 'image',
        src: img.src,
        alt: img.alt || 'Image'
      });
    });

    // Check for documents
    const documents = messageElement.querySelectorAll('[data-testid="msg-document"]');
    documents.forEach(doc => {
      const fileName = doc.querySelector('[data-testid="document-name"]')?.textContent || 'Document';
      const fileSize = doc.querySelector('[data-testid="document-size"]')?.textContent || '';
      attachments.push({
        type: 'document',
        fileName: fileName,
        fileSize: fileSize
      });
    });

    // Check for audio messages
    const audioElements = messageElement.querySelectorAll('[data-testid="msg-audio"]');
    audioElements.forEach(audio => {
      const duration = audio.querySelector('[data-testid="audio-duration"]')?.textContent || '';
      attachments.push({
        type: 'audio',
        duration: duration
      });
    });

    // Check for videos
    const videos = messageElement.querySelectorAll('video');
    videos.forEach(video => {
      attachments.push({
        type: 'video',
        src: video.src,
        duration: video.duration || 'Unknown'
      });
    });

    return attachments;
  }

  // Detect message direction (incoming/outgoing)
  detectMessageDirection(messageElement) {
    // Multiple ways to detect outgoing messages
    const outgoingIndicators = [
      () => messageElement.classList.contains('message-out'),
      () => messageElement.querySelector('[data-testid="msg-meta-outgoing"]'),
      () => messageElement.closest('.message-out'),
      () => messageElement.querySelector('[data-testid="tail-out"]'),
      () => messageElement.querySelector('.tail-container-out'),
      () => {
        // Check if message is on the right side (outgoing)
        const rect = messageElement.getBoundingClientRect();
        const parentRect = messageElement.parentElement?.getBoundingClientRect();
        if (rect && parentRect) {
          const centerX = rect.left + rect.width / 2;
          const parentCenterX = parentRect.left + parentRect.width / 2;
          return centerX > parentCenterX;
        }
        return false;
      }
    ];

    for (const indicator of outgoingIndicators) {
      try {
        if (indicator()) {
          return true;
        }
      } catch (e) {
        // Continue to next indicator
      }
    }

    return false;
  }

  // Extract sender name (especially useful for group chats)
  extractSenderName(messageElement, contactName, isOutgoing) {
    if (isOutgoing) {
      return 'You';
    }

    // For incoming messages in individual chats, use the contact name
    // For group chats, try to find the actual sender name

    // First check if this is a group chat by looking for sender names in messages
    const senderSelectors = [
      '[data-testid="msg-meta"] span[dir="auto"]',
      '.copyable-text[data-testid="msg-meta"] span',
      '[data-testid="msg-meta"] .copyable-text',
      'span[dir="auto"][style*="color"]',
      '.message-author',
      '[data-testid="sender-name"]'
    ];

    for (const selector of senderSelectors) {
      const senderElement = messageElement.querySelector(selector);
      if (senderElement) {
        const senderText = senderElement.textContent || senderElement.innerText || '';
        if (senderText &&
            senderText.trim() &&
            !senderText.match(/\d{1,2}:\d{2}/) &&
            !senderText.includes('search') &&
            !senderText.includes('refresh') &&
            senderText.length > 1 &&
            senderText.length < 50) {
          console.log(`Found sender: ${senderText.trim()}`);
          return senderText.trim();
        }
      }
    }

    // If no specific sender found and we have a valid contact name, use it
    if (contactName &&
        contactName !== 'Unknown' &&
        contactName !== 'search-refreshed' &&
        !contactName.includes('search') &&
        !contactName.includes('refresh')) {
      return contactName;
    }

    // Last resort: try to get a clean contact name from the header again
    const cleanContactName = this.getCleanContactName();
    if (cleanContactName && cleanContactName !== 'Unknown') {
      return cleanContactName;
    }

    return 'Unknown Contact';
  }

  // Get a clean contact name, avoiding UI text
  getCleanContactName() {
    const mainArea = document.querySelector('#main, [data-testid="main"]');
    if (!mainArea) return 'Unknown';

    // Look specifically in the header area for the contact name
    const headerArea = mainArea.querySelector('header, [data-testid="conversation-header"]');
    if (headerArea) {
      const nameElements = headerArea.querySelectorAll('span, h1, div');
      for (const element of nameElements) {
        const text = element.textContent?.trim();
        if (text &&
            text.length > 1 &&
            text.length < 50 &&
            !text.match(/^\+?[\d\s\-\(\)]+$/) &&
            !text.match(/\d{1,2}:\d{2}/) &&
            !text.includes('WhatsApp') &&
            !text.includes('search') &&
            !text.includes('refresh') &&
            !text.includes('Search') &&
            !text.includes('Menu') &&
            !text.includes('Back') &&
            !text.includes('Type') &&
            !text.includes('Emoji') &&
            !text.includes('online') &&
            !text.includes('last seen')) {

          // Make sure this element is visible and not a UI element
          const rect = element.getBoundingClientRect();
          if (rect.width > 50 && rect.height > 10) {
            console.log(`Found clean contact name: ${text}`);
            return text;
          }
        }
      }
    }

    return 'Unknown';
  }

  // Extract message status with better detection
  extractMessageStatus(messageElement, isOutgoing) {
    if (!isOutgoing) {
      return 'received';
    }

    // Look for status indicators
    const statusSelectors = [
      '[data-testid="msg-dblcheck"]',
      '[data-testid="msg-check"]',
      '[data-testid="msg-time"] + span',
      '.msg-check',
      '.msg-dblcheck'
    ];

    for (const selector of statusSelectors) {
      const statusElement = messageElement.querySelector(selector);
      if (statusElement) {
        const testId = statusElement.getAttribute('data-testid');

        if (testId === 'msg-dblcheck') {
          // Double check mark - delivered or read
          if (statusElement.classList.contains('read') ||
              statusElement.style.color === 'rgb(83, 188, 254)' ||
              statusElement.querySelector('[fill="#53bcfe"]')) {
            return 'read';
          }
          return 'delivered';
        } else if (testId === 'msg-check') {
          return 'sent';
        }
      }
    }

    return 'sent';
  }

  // Extract phone number from contact info
  extractPhoneNumber(headerElement) {
    console.log('Extracting phone number from header:', headerElement);

    // Try to extract from URL first (most reliable)
    const url = window.location.href;
    console.log('Current URL:', url);

    // WhatsApp Web URL patterns
    const urlPatterns = [
      /\/chat\/(\+?[\d]+)/,
      /phone=(\+?[\d]+)/,
      /\/(\+?[\d]{10,15})/
    ];

    for (const pattern of urlPatterns) {
      const match = url.match(pattern);
      if (match && match[1]) {
        const phone = match[1];
        if (phone.length >= 10) {
          console.log(`Found phone number from URL: ${phone}`);
          return phone;
        }
      }
    }

    if (!headerElement) {
      console.log('No header element for phone extraction');
      return this.getPhoneFromAlternativeSources();
    }

    // Try to find phone number in header elements
    const phoneSelectors = [
      'span[title*="+"]',
      'span[dir="auto"]',
      '[data-testid="conversation-info-header"] span',
      'span[title]',
      'div[title]'
    ];

    for (const selector of phoneSelectors) {
      const phoneElements = headerElement.querySelectorAll(selector);
      for (let element of phoneElements) {
        const sources = [
          element.getAttribute('title'),
          element.getAttribute('data-title'),
          element.textContent,
          element.innerText
        ];

        for (const text of sources) {
          if (text && /^\+?[\d\s\-\(\)]{10,}$/.test(text.trim())) {
            const cleanPhone = text.trim().replace(/[\s\-\(\)]/g, '');
            if (cleanPhone.length >= 10) {
              console.log(`Found phone number: ${cleanPhone} using selector: ${selector}`);
              return cleanPhone;
            }
          }
        }
      }
    }

    return this.getPhoneFromAlternativeSources();
  }

  // Get phone number from alternative sources
  getPhoneFromAlternativeSources() {
    // Try to find phone numbers anywhere in the page
    const allElements = document.querySelectorAll('span, div');
    for (const element of allElements) {
      const text = element.textContent || element.getAttribute('title') || '';
      if (text && /^\+?[\d\s\-\(\)]{10,}$/.test(text.trim())) {
        const cleanPhone = text.trim().replace(/[\s\-\(\)]/g, '');
        if (cleanPhone.length >= 10 && cleanPhone.length <= 15) {
          console.log(`Found phone number from page scan: ${cleanPhone}`);
          return cleanPhone;
        }
      }
    }

    console.log('Could not find phone number');
    return 'Unknown';
  }

  // Get all contacts from chat list
  async extractAllContacts() {
    try {
      await this.waitForElement('[data-testid="chat-list"]');

      const chatList = document.querySelector('[data-testid="chat-list"]');
      const chatElements = chatList.querySelectorAll('[data-testid="cell-frame-container"]');

      const contacts = [];
      chatElements.forEach(chatElement => {
        const nameElement = chatElement.querySelector('[data-testid="conversation-info-header"] span[title]');
        const lastMessageElement = chatElement.querySelector('[data-testid="last-msg-text"]');
        const timeElement = chatElement.querySelector('[data-testid="msg-meta-time"]');

        if (nameElement) {
          contacts.push({
            name: nameElement.textContent,
            lastMessage: lastMessageElement ? lastMessageElement.textContent : '',
            lastMessageTime: timeElement ? timeElement.textContent : '',
            phoneNumber: 'Unknown' // Phone numbers are not easily accessible from chat list
          });
        }
      });

      return contacts;
    } catch (error) {
      console.error('Error extracting contacts:', error);
      return [];
    }
  }

  // Get page structure for debugging
  getPageStructure() {
    const structure = {
      hasApp: false,
      hasQR: false,
      hasChat: false,
      messageCount: 0,
      contactName: 'Unknown',
      selectors: []
    };

    // Check for app container
    const appSelectors = [
      '[data-testid="app-wrapper-web"]',
      '.app-wrapper-web',
      '#app',
      '[data-testid="main"]'
    ];

    for (const selector of appSelectors) {
      if (document.querySelector(selector)) {
        structure.hasApp = true;
        structure.selectors.push(`App: ${selector}`);
        break;
      }
    }

    // Check for QR code
    const qrCode = document.querySelector('[data-testid="qr-code"]');
    structure.hasQR = qrCode && qrCode.offsetParent !== null;

    // Check for chat container
    const messageSelectors = [
      '[data-testid="conversation-panel-messages"]',
      '[data-testid="main"] [role="application"]',
      '#main [data-testid="conversation-panel-messages"]',
      '.copyable-area [role="application"]',
      '#main .copyable-area',
      '[data-testid="main"] .copyable-area'
    ];

    let messagesContainer = null;
    for (const selector of messageSelectors) {
      messagesContainer = document.querySelector(selector);
      if (messagesContainer) {
        structure.hasChat = true;
        structure.selectors.push(`Messages: ${selector}`);
        break;
      }
    }

    // Count messages if chat is open
    if (messagesContainer) {
      const msgSelectors = [
        '[data-testid="msg-container"]',
        '[data-testid="msg"]',
        '.message-in, .message-out',
        'div[data-id]'
      ];

      for (const selector of msgSelectors) {
        const messages = messagesContainer.querySelectorAll(selector);
        if (messages.length > 0) {
          structure.messageCount = messages.length;
          structure.selectors.push(`Msg elements: ${selector} (${messages.length})`);
          break;
        }
      }
    }

    // Get contact name and phone number
    const headerSelectors = [
      '[data-testid="conversation-header"]',
      'header[data-testid="conversation-header"]',
      '[data-testid="chat-header"]',
      'header',
      '[role="banner"]'
    ];

    for (const selector of headerSelectors) {
      const header = document.querySelector(selector);
      if (header) {
        structure.contactName = this.extractContactName(header);
        structure.phoneNumber = this.extractPhoneNumber(header);
        structure.selectors.push(`Header: ${selector}`);
        break;
      }
    }

    // Add current URL for debugging
    structure.currentUrl = window.location.href;
    structure.pageTitle = document.title;

    return structure;
  }
}

// Initialize extractor
const extractor = new WhatsAppDataExtractor();

// Listen for messages from popup
chrome.runtime.onMessage.addListener((request, sender, sendResponse) => {
  // Handle ping to check if content script is loaded
  if (request.action === 'ping') {
    sendResponse({ success: true, message: 'Content script is ready' });
    return;
  }

  if (request.action === 'getPageStructure') {
    try {
      const structure = extractor.getPageStructure();
      sendResponse({ success: true, data: structure });
    } catch (error) {
      sendResponse({ success: false, error: error.message });
    }
    return;
  }

  if (request.action === 'extractCurrentChat') {
    extractor.extractCurrentChatData()
      .then(data => sendResponse({ success: true, data: data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true; // Keep message channel open for async response
  }

  if (request.action === 'extractAllContacts') {
    extractor.extractAllContacts()
      .then(data => sendResponse({ success: true, data: data }))
      .catch(error => sendResponse({ success: false, error: error.message }));
    return true;
  }
});

console.log('WhatsApp Web Data Extractor loaded');
