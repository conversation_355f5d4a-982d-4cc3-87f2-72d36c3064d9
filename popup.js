// WhatsApp Data Exporter Popup Script

class WhatsAppExporter {
    constructor() {
        this.initializeElements();
        this.attachEventListeners();
        this.checkWhatsAppTab();
    }

    initializeElements() {
        this.statusElement = document.getElementById('status');
        this.progressContainer = document.getElementById('progressContainer');
        this.progressFill = document.getElementById('progressFill');
        this.progressText = document.getElementById('progressText');

        this.debugInfoBtn = document.getElementById('debugInfo');
        this.exportCurrentChatBtn = document.getElementById('exportCurrentChat');
        this.exportAllContactsBtn = document.getElementById('exportAllContacts');

        this.includeMessages = document.getElementById('includeMessages');
        this.includeTimestamps = document.getElementById('includeTimestamps');
        this.includeAttachments = document.getElementById('includeAttachments');
        this.includePhoneNumbers = document.getElementById('includePhoneNumbers');
    }

    attachEventListeners() {
        this.debugInfoBtn.addEventListener('click', () => this.showDebugInfo());
        this.exportCurrentChatBtn.addEventListener('click', () => this.exportCurrentChat());
        this.exportAllContactsBtn.addEventListener('click', () => this.exportAllContacts());
    }

    async checkWhatsAppTab() {
        try {
            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            if (!tab.url.includes('web.whatsapp.com')) {
                this.showStatus('Please open web.whatsapp.com to use this extension', 'error');
                this.disableButtons();
                return;
            }

            // Test connection to content script
            try {
                await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
                this.showStatus('Ready to export WhatsApp data', 'success');
            } catch (connectionError) {
                this.showStatus('Content script not ready. Please refresh the WhatsApp page.', 'error');
                this.disableButtons();
                console.error('Content script connection error:', connectionError);
            }
        } catch (error) {
            this.showStatus('Error checking current tab', 'error');
            console.error('Error:', error);
        }
    }

    showStatus(message, type = 'info') {
        this.statusElement.innerHTML = `<p>${message}</p>`;
        this.statusElement.className = `status ${type}`;
    }

    showProgress(percentage, text) {
        this.progressContainer.style.display = 'block';
        this.progressFill.style.width = `${percentage}%`;
        this.progressText.textContent = text;
    }

    hideProgress() {
        this.progressContainer.style.display = 'none';
    }

    disableButtons() {
        this.debugInfoBtn.disabled = true;
        this.exportCurrentChatBtn.disabled = true;
        this.exportAllContactsBtn.disabled = true;
    }

    enableButtons() {
        this.debugInfoBtn.disabled = false;
        this.exportCurrentChatBtn.disabled = false;
        this.exportAllContactsBtn.disabled = false;
    }

    async showDebugInfo() {
        try {
            this.showStatus('Gathering debug information...', 'info');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            let debugInfo = `🔍 Debug Information:\n\n`;
            debugInfo += `📍 Current URL: ${tab.url}\n`;
            debugInfo += `📱 Tab Title: ${tab.title}\n`;
            debugInfo += `🆔 Tab ID: ${tab.id}\n\n`;

            if (!tab.url.includes('web.whatsapp.com')) {
                debugInfo += `❌ Not on WhatsApp Web\n`;
                debugInfo += `➡️ Please navigate to web.whatsapp.com\n`;
            } else {
                debugInfo += `✅ On WhatsApp Web\n\n`;

                try {
                    // Test content script connection
                    const pingResponse = await chrome.tabs.sendMessage(tab.id, { action: 'ping' });
                    debugInfo += `✅ Content script responsive\n`;
                    debugInfo += `📝 Response: ${pingResponse.message}\n\n`;

                    // Get page structure info
                    const structureResponse = await chrome.tabs.sendMessage(tab.id, { action: 'getPageStructure' });
                    if (structureResponse.success) {
                        debugInfo += `📊 Page Structure:\n`;
                        debugInfo += `• App container: ${structureResponse.data.hasApp ? '✅' : '❌'}\n`;
                        debugInfo += `• QR code visible: ${structureResponse.data.hasQR ? '❌' : '✅'}\n`;
                        debugInfo += `• Chat open: ${structureResponse.data.hasChat ? '✅' : '❌'}\n`;
                        debugInfo += `• Messages found: ${structureResponse.data.messageCount}\n`;
                        debugInfo += `• Contact name: ${structureResponse.data.contactName}\n\n`;

                        if (structureResponse.data.selectors) {
                            debugInfo += `🎯 Available selectors:\n`;
                            structureResponse.data.selectors.forEach(sel => {
                                debugInfo += `• ${sel}\n`;
                            });
                        }
                    }

                } catch (contentError) {
                    debugInfo += `❌ Content script not responding\n`;
                    debugInfo += `🔧 Error: ${contentError.message}\n`;
                    debugInfo += `💡 Try refreshing the WhatsApp page\n`;
                }
            }

            // Show debug info in a popup
            alert(debugInfo);
            this.showStatus('Debug information gathered', 'success');

        } catch (error) {
            this.showStatus(`Debug failed: ${error.message}`, 'error');
            console.error('Debug error:', error);
        }
    }

    async exportCurrentChat() {
        try {
            this.disableButtons();
            this.showStatus('Extracting chat data...', 'info');
            this.showProgress(20, 'Connecting to WhatsApp Web...');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Ensure content script is loaded
            await this.ensureContentScriptLoaded(tab.id);

            this.showProgress(40, 'Extracting messages...');

            const response = await this.sendMessageWithRetry(tab.id, { action: 'extractCurrentChat' });

            if (!response.success) {
                throw new Error(response.error);
            }

            this.showProgress(70, 'Processing data...');

            const excelData = this.prepareExcelData(response.data);

            this.showProgress(90, 'Generating Excel file...');

            await this.downloadExcel(excelData, `WhatsApp_Chat_${response.data.contact}_${new Date().toISOString().split('T')[0]}.xlsx`);

            this.showProgress(100, 'Export completed!');
            this.showStatus(`Successfully exported ${response.data.messages.length} messages`, 'success');

            setTimeout(() => {
                this.hideProgress();
                this.enableButtons();
            }, 2000);

        } catch (error) {
            this.hideProgress();
            this.enableButtons();
            this.showStatus(`Export failed: ${error.message}`, 'error');
            console.error('Export error:', error);
        }
    }

    async exportAllContacts() {
        try {
            this.disableButtons();
            this.showStatus('Extracting contacts...', 'info');
            this.showProgress(20, 'Connecting to WhatsApp Web...');

            const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });

            // Ensure content script is loaded
            await this.ensureContentScriptLoaded(tab.id);

            this.showProgress(50, 'Extracting contact list...');

            const response = await this.sendMessageWithRetry(tab.id, { action: 'extractAllContacts' });

            if (!response.success) {
                throw new Error(response.error);
            }

            this.showProgress(80, 'Processing contacts...');

            const excelData = this.prepareContactsExcelData(response.data);

            this.showProgress(95, 'Generating Excel file...');

            await this.downloadExcel(excelData, `WhatsApp_Contacts_${new Date().toISOString().split('T')[0]}.xlsx`);

            this.showProgress(100, 'Export completed!');
            this.showStatus(`Successfully exported ${response.data.length} contacts`, 'success');

            setTimeout(() => {
                this.hideProgress();
                this.enableButtons();
            }, 2000);

        } catch (error) {
            this.hideProgress();
            this.enableButtons();
            this.showStatus(`Export failed: ${error.message}`, 'error');
            console.error('Export error:', error);
        }
    }

    prepareExcelData(chatData) {
        const worksheets = {};
        
        // Messages worksheet
        if (this.includeMessages.checked) {
            const messagesData = chatData.messages.map(msg => {
                const row = {
                    'Contact': msg.contact,
                    'Sender': msg.sender,
                    'Message': msg.message,
                    'Message Type': msg.messageType,
                    'Direction': msg.isOutgoing ? 'Outgoing' : 'Incoming',
                    'Status': msg.status
                };
                
                if (this.includeTimestamps.checked) {
                    row['Timestamp'] = msg.timestamp;
                }
                
                if (this.includePhoneNumbers.checked) {
                    row['Phone Number'] = msg.phoneNumber;
                }
                
                if (this.includeAttachments.checked && msg.attachments.length > 0) {
                    row['Attachments'] = msg.attachments.map(att => {
                        if (att.type === 'document') {
                            return `${att.type}: ${att.fileName} (${att.fileSize})`;
                        } else if (att.type === 'audio') {
                            return `${att.type}: ${att.duration}`;
                        } else if (att.type === 'video') {
                            return `${att.type}: ${att.duration}`;
                        } else {
                            return `${att.type}: ${att.alt || 'Media'}`;
                        }
                    }).join('; ');
                }
                
                return row;
            });
            
            worksheets['Messages'] = messagesData;
        }
        
        // Attachments worksheet
        if (this.includeAttachments.checked) {
            const attachmentsData = [];
            chatData.messages.forEach(msg => {
                msg.attachments.forEach(att => {
                    const row = {
                        'Contact': msg.contact,
                        'Sender': msg.sender,
                        'Type': att.type,
                        'Timestamp': this.includeTimestamps.checked ? msg.timestamp : 'N/A'
                    };
                    
                    if (att.type === 'document') {
                        row['File Name'] = att.fileName;
                        row['File Size'] = att.fileSize;
                    } else if (att.type === 'audio' || att.type === 'video') {
                        row['Duration'] = att.duration;
                    } else if (att.type === 'image') {
                        row['Description'] = att.alt;
                    }
                    
                    if (this.includePhoneNumbers.checked) {
                        row['Phone Number'] = msg.phoneNumber;
                    }
                    
                    attachmentsData.push(row);
                });
            });
            
            if (attachmentsData.length > 0) {
                worksheets['Attachments'] = attachmentsData;
            }
        }
        
        return worksheets;
    }

    prepareContactsExcelData(contactsData) {
        const contactsFormatted = contactsData.map(contact => {
            const row = {
                'Contact Name': contact.name,
                'Last Message': contact.lastMessage,
                'Last Message Time': contact.lastMessageTime
            };
            
            if (this.includePhoneNumbers.checked) {
                row['Phone Number'] = contact.phoneNumber;
            }
            
            return row;
        });
        
        return { 'Contacts': contactsFormatted };
    }

    async downloadExcel(worksheets, filename) {
        // For now, use CSV export as it's more reliable and doesn't require external libraries
        // CSV files can be opened in Excel and work just as well for data analysis
        console.log('Using CSV export for better compatibility');
        await this.downloadCSV(worksheets, filename.replace('.xlsx', '.csv'));
    }

    // Enhanced CSV export that handles multiple worksheets
    async downloadCSV(worksheets, filename) {
        const worksheetNames = Object.keys(worksheets);

        if (worksheetNames.length === 0) {
            throw new Error('No data to export');
        }

        // If multiple worksheets, create a combined CSV or download separately
        if (worksheetNames.length === 1) {
            // Single worksheet - simple CSV
            await this.downloadSingleCSV(worksheets[worksheetNames[0]], filename);
        } else {
            // Multiple worksheets - create combined CSV with section headers
            await this.downloadCombinedCSV(worksheets, filename);
        }

        this.showStatus(`Data exported as CSV format (${worksheetNames.length} sheet${worksheetNames.length > 1 ? 's' : ''})`, 'success');
    }

    // Download single worksheet as CSV
    async downloadSingleCSV(data, filename) {
        if (!data || data.length === 0) {
            throw new Error('No data to export');
        }

        const csvContent = this.convertToCSV(data);
        this.downloadFile(csvContent, filename, 'text/csv;charset=utf-8;');
    }

    // Download multiple worksheets as combined CSV
    async downloadCombinedCSV(worksheets, filename) {
        let combinedContent = '';

        Object.keys(worksheets).forEach((sheetName, index) => {
            const data = worksheets[sheetName];

            if (data && data.length > 0) {
                // Add section header
                if (index > 0) combinedContent += '\n\n';
                combinedContent += `=== ${sheetName.toUpperCase()} ===\n`;

                // Add CSV data for this sheet
                combinedContent += this.convertToCSV(data);
            }
        });

        this.downloadFile(combinedContent, filename, 'text/csv;charset=utf-8;');
    }

    // Convert data array to CSV format
    convertToCSV(data) {
        if (!data || data.length === 0) return '';

        // Get headers
        const headers = Object.keys(data[0]);

        // Create CSV content
        let csvContent = headers.join(',') + '\n';

        data.forEach(row => {
            const values = headers.map(header => {
                let value = row[header] || '';
                // Convert to string and escape
                value = value.toString();
                // Escape commas, quotes, and newlines
                if (value.includes(',') || value.includes('"') || value.includes('\n')) {
                    value = '"' + value.replace(/"/g, '""') + '"';
                }
                return value;
            });
            csvContent += values.join(',') + '\n';
        });

        return csvContent;
    }

    // Generic file download helper
    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Ensure content script is loaded and responsive
    async ensureContentScriptLoaded(tabId) {
        try {
            // Try to ping the content script
            await chrome.tabs.sendMessage(tabId, { action: 'ping' });
        } catch (error) {
            // If ping fails, inject the content script manually
            console.log('Content script not responding, injecting...');
            await chrome.scripting.executeScript({
                target: { tabId: tabId },
                files: ['content.js']
            });

            // Wait a bit for the script to initialize
            await new Promise(resolve => setTimeout(resolve, 1000));

            // Try ping again
            await chrome.tabs.sendMessage(tabId, { action: 'ping' });
        }
    }

    // Send message with retry logic
    async sendMessageWithRetry(tabId, message, maxRetries = 3) {
        for (let i = 0; i < maxRetries; i++) {
            try {
                const response = await chrome.tabs.sendMessage(tabId, message);
                return response;
            } catch (error) {
                console.log(`Message attempt ${i + 1} failed:`, error);

                if (i === maxRetries - 1) {
                    throw new Error('Failed to communicate with WhatsApp page. Please refresh the page and try again.');
                }

                // Wait before retry
                await new Promise(resolve => setTimeout(resolve, 1000));

                // Try to ensure content script is loaded
                try {
                    await this.ensureContentScriptLoaded(tabId);
                } catch (injectionError) {
                    console.log('Content script injection failed:', injectionError);
                }
            }
        }
    }
}

// Initialize the exporter when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WhatsAppExporter();
});
