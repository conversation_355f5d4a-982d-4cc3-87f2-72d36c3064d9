# WhatsApp Web Data Exporter

A Chrome extension that allows you to export WhatsApp Web chat data, contact information, and attachment details to Excel spreadsheets.

## Features

- 📱 Export individual chat conversations
- 📋 Export all contacts list
- 📎 Include attachment details (images, documents, audio, video)
- ⏰ Include timestamps for all messages
- 📞 Extract phone numbers when available
- 📊 Export to Excel format (.xlsx)
- 🎨 User-friendly popup interface

## Installation

### Method 1: Load Unpacked Extension (Developer Mode)

1. **Download or Clone** this repository to your local machine
2. **Open Chrome** and navigate to `chrome://extensions/`
3. **Enable Developer Mode** by toggling the switch in the top-right corner
4. **Click "Load unpacked"** and select the folder containing the extension files
5. **Pin the extension** to your toolbar for easy access

### Method 2: Create Icons (Optional)

If you want custom icons, create the following files in the `icons/` directory:
- `icon16.png` (16x16 pixels)
- `icon48.png` (48x48 pixels) 
- `icon128.png` (128x128 pixels)

You can use any image editing software or online icon generators.

## Usage

### Exporting Current Chat

1. **Open WhatsApp Web** in a Chrome tab (`https://web.whatsapp.com`)
2. **Navigate to the chat** you want to export
3. **Click the extension icon** in your Chrome toolbar
4. **Select export options** (messages, timestamps, attachments, phone numbers)
5. **Click "Export Current Chat"**
6. **Wait for processing** and the Excel file will download automatically

### Exporting All Contacts

1. **Open WhatsApp Web** in a Chrome tab
2. **Click the extension icon** in your Chrome toolbar
3. **Click "Export All Contacts"**
4. **Wait for processing** and the Excel file will download automatically

## Export Options

- **Include Messages**: Export all text messages from the chat
- **Include Timestamps**: Add timestamp information for each message
- **Include Attachment Details**: Export information about media files, documents, etc.
- **Include Phone Numbers**: Extract and include phone numbers when available

## Excel Output Format

### Messages Sheet
- Contact Name
- Sender (You/Contact Name)
- Message Text
- Message Type (text/media)
- Direction (Incoming/Outgoing)
- Status (sent/delivered/read)
- Timestamp (if enabled)
- Phone Number (if enabled)
- Attachments (if enabled)

### Attachments Sheet (if enabled)
- Contact Name
- Sender
- Attachment Type (image/document/audio/video)
- File Details (name, size, duration)
- Timestamp
- Phone Number (if enabled)

### Contacts Sheet
- Contact Name
- Last Message
- Last Message Time
- Phone Number (if available)

## Limitations

- Only works with **web.whatsapp.com**
- Cannot export media files themselves (only metadata)
- Phone numbers may not always be available due to WhatsApp's privacy settings
- Large chats may take longer to process
- Requires WhatsApp Web to be loaded and active

## Privacy & Security

- **No data is sent to external servers** - all processing happens locally in your browser
- **No personal information is stored** by the extension
- **Data is only exported to files you choose to download**
- Extension only has access to WhatsApp Web pages

## Troubleshooting

### Extension Not Working
- Make sure you're on `web.whatsapp.com`
- Refresh the WhatsApp Web page
- Try reloading the extension in `chrome://extensions/`

### Export Fails
- Ensure WhatsApp Web is fully loaded
- Try scrolling through the chat to load more messages
- Check if you have sufficient storage space for the download

### Missing Data
- Some data may not be available due to WhatsApp's structure
- Phone numbers are often not visible in the web interface
- Very old messages might not be loaded in the current view

## Technical Details

- **Manifest Version**: 3
- **Permissions**: activeTab, storage, downloads
- **Libraries**: SheetJS (XLSX) for Excel generation
- **Compatible with**: Chrome, Edge, and other Chromium-based browsers

## Contributing

Feel free to submit issues, feature requests, or pull requests to improve this extension.

## License

This project is open source and available under the MIT License.

## Disclaimer

This extension is not affiliated with WhatsApp or Meta. Use responsibly and in accordance with WhatsApp's Terms of Service.
